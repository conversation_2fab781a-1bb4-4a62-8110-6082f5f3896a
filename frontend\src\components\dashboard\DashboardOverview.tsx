import React from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getDashboardStatsAPI, getDashboardStatsWithErrorsAPI } from "@/lib/api";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { FileText, BookOpen, FileQuestion, Trophy, AlertTriangle } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { useNetworkStatus } from "@/hooks/useNetworkStatus";
import DashboardErrorState from "./DashboardErrorState";
import { commonNotifications } from "@/lib/notifications";

interface StatCardProps {
  title: string;
  value: string | number;
  description: string;
  icon: React.ReactElement;
  isLoading?: boolean;
  hasError?: boolean;
  errorMessage?: string;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  description,
  icon,
  isLoading,
  hasError,
  errorMessage,
}) => {
  const baseCardClasses = "shadow-lg transition-all duration-300 ease-in-out";
  const cardClasses = hasError
    ? `${baseCardClasses} bg-slate-800 border-orange-500/50 text-orange-400`
    : `${baseCardClasses} bg-slate-800 border-slate-700 text-purple-400 hover:shadow-xl transform hover:-translate-y-1 group`;

  const iconElementClasses = hasError ? "h-5 w-5 text-orange-400" : "h-5 w-5 text-purple-400";

  if (isLoading) {
    return (
      <Card className={`${baseCardClasses} bg-slate-800 border-slate-700 text-purple-400`}>
        <CardContent className="p-5 flex flex-col space-y-2">
          <div className="flex items-center justify-between w-full">
            <Skeleton className="h-5 w-3/5 bg-slate-700" />
            <Skeleton className="h-9 w-9 rounded-lg bg-slate-700" />
          </div>
          <Skeleton className="h-8 w-2/5 bg-slate-700" />
          <Skeleton className="h-4 w-4/5 bg-slate-700" />
        </CardContent>
      </Card>
    );
  }

  const titleClasses = "text-base font-medium text-slate-300";
  const valueClasses = hasError
    ? "text-2xl font-bold text-orange-400"
    : "text-2xl font-bold text-purple-400 group-hover:text-purple-300 transition-colors";
  const descriptionClasses = hasError
    ? "text-xs text-orange-300"
    : "text-xs text-slate-400 group-hover:text-slate-300 transition-colors";
  const iconContainerClasses = hasError
    ? "p-2 bg-orange-500/20 rounded-lg flex items-center justify-center"
    : "p-2 bg-purple-500/20 group-hover:bg-purple-500/30 transition-colors rounded-lg flex items-center justify-center";

  return (
    <Card className={cardClasses}>
      <CardContent className="p-5 flex flex-col space-y-2">
        <div className="flex items-start justify-between w-full">
          <h3 className={titleClasses}>{title}</h3>
          <div className={iconContainerClasses}>
            {hasError ? (
              <AlertTriangle className={iconElementClasses} />
            ) : (
              React.cloneElement(icon, { className: iconElementClasses })
            )}
          </div>
        </div>
        <p className={valueClasses}>
          {hasError ? "Partial" : value}
        </p>
        <p className={descriptionClasses}>
          {hasError ? (errorMessage || "Some data unavailable") : description}
        </p>
      </CardContent>
    </Card>
  );
};

const DashboardOverview: React.FC = () => {
  const { user, loading: authLoading } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const networkStatus = useNetworkStatus();

  // Enhanced query with better error handling and partial failure support
  const {
    data: stats,
    isLoading,
    error,
    refetch,
    isRefetching
  } = useQuery({
    queryKey: ["dashboard-stats"],
    queryFn: async () => {
      try {
        // Try to get stats with error details first
        const result = await getDashboardStatsWithErrorsAPI();

        // Show warning toast for partial failures with details
        if (result.hasPartialFailure) {
          commonNotifications.dashboardPartialFailure(result.errors);
        }

        return result.data;
      } catch (error) {
        // Fallback to regular API if enhanced version fails
        console.warn("Enhanced API failed, falling back to regular API:", error);
        return await getDashboardStatsAPI();
      }
    },
    retry: (failureCount, error) => {
      // Don't retry if it's a complete failure after 3 attempts
      if (failureCount >= 3) return false;

      // Don't retry on authentication errors
      if (error?.message?.includes('401') || error?.message?.includes('403')) {
        return false;
      }

      return true;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
    enabled: !!user && !authLoading && networkStatus.isOnline,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnReconnect: true,
  });

  // Auto-refresh when network comes back online
  React.useEffect(() => {
    if (networkStatus.isOnline && user && !authLoading && !isLoading) {
      // Small delay to ensure connection is stable
      const timer = setTimeout(() => {
        refetch();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [networkStatus.isOnline, user, authLoading, isLoading, refetch]);

  // Retry handler with network status check
  const handleRetry = React.useCallback(() => {
    if (!networkStatus.isOnline) {
      commonNotifications.networkError();
      return;
    }

    refetch().then(() => {
      commonNotifications.dashboardRetrySuccess();
    }).catch(() => {
      commonNotifications.dashboardRetryFailed();
    });
  }, [refetch, networkStatus.isOnline]);

  const overviewItems = [
    {
      title: "Documents",
      getValue: (s: any) => s?.totalDocuments || 0,
      description: "Total uploaded",
      icon: <FileText />,
    },
    {
      title: "Total Flashcards",
      getValue: (s: any) => s?.totalFlashcards || 0,
      description: "All flashcards created",
      icon: <BookOpen />,
    },
    {
      title: "Total Quizzes",
      getValue: (s: any) => s?.totalQuizzes || 0,
      description: "Quizzes created",
      icon: <FileQuestion />,
    },
    {
      title: "Completions",
      getValue: (s: any) => s?.totalCompletions || 0,
      description: "Quizzes/sets completed",
      icon: <Trophy />,
    },
  ];

  // Show loading state while auth is loading
  if (authLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {[...Array(4)].map((_, i) => (
          <StatCard
            key={`auth-loading-${i}`}
            title=""
            value=""
            description=""
            icon={<div />}
            isLoading={true}
          />
        ))}
      </div>
    );
  }

  // Show placeholder state when user is not authenticated
  if (!user) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {overviewItems.map((item) => (
          <Card key={item.title} className="bg-slate-800 border-slate-700 text-purple-400">
            <CardContent className="p-5 flex flex-col space-y-2">
              <div className="flex items-start justify-between w-full">
                <h3 className="text-base font-medium text-slate-300">{item.title}</h3>
                <div className="p-2 bg-slate-600/20 rounded-lg flex items-center justify-center">
                  {React.cloneElement(item.icon, { className: "h-5 w-5 text-slate-400" })}
                </div>
              </div>
              <p className="text-2xl font-bold text-slate-400">--</p>
              <p className="text-xs text-slate-400">Please sign in</p>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Show comprehensive error state if there's a complete failure
  if (error) {
    console.error("Dashboard stats error:", error);
    return (
      <DashboardErrorState
        error={error as Error}
        onRetry={handleRetry}
        isRetrying={isRefetching}
        showNetworkStatus={true}
      />
    );
  }

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {[...Array(4)].map((_, i) => (
          <StatCard
            key={`skeleton-${i}`}
            title=""
            value=""
            description=""
            icon={<div />}
            isLoading={true}
          />
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {overviewItems.map((item) => (
        <StatCard
          key={item.title}
          title={item.title}
          value={item.getValue(stats)}
          description={item.description}
          icon={item.icon}
          isLoading={false}
        />
      ))}
    </div>
  );
};

export default DashboardOverview;
