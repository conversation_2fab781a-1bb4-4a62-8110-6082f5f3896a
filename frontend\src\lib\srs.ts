import { Flashcard } from "@/types";
import { QuizQuestion } from "@shared/types/quiz";
import { Tables } from "@/types/supabase";

// Constants for the SuperMemo SM-2 algorithm
const INITIAL_EASE_FACTOR = 2.5;
const MINIMUM_EASE_FACTOR = 1.3;
const EASE_FACTOR_MODIFIER = 0.15;

// Difficulty levels
export enum ReviewDifficulty {
  DIFFICULT = 0,
  MEDIUM = 1,
  EASY = 2,
}

/**
 * Implements the SuperMemo SM-2 algorithm for spaced repetition
 * Based on the algorithm documented at: https://www.supermemo.com/en/archives1990-2015/english/ol/sm2
 */
export function updateCardWithSpacedRepetition(
  card: Flashcard,
  difficulty: ReviewDifficulty
): Flashcard {
  const now = Date.now();
  let { interval, easinessFactor, reviewCount, correct, incorrect } = card;

  // Initialize values if this is the first review
  interval = interval || 0;
  easinessFactor = easinessFactor || INITIAL_EASE_FACTOR;
  reviewCount = reviewCount || 0;
  correct = correct || 0;
  incorrect = incorrect || 0;

  // Update correct/incorrect count
  if (difficulty === ReviewDifficulty.DIFFICULT) {
    incorrect += 1;
  } else {
    correct += 1;
  }

  // Calculate the new interval and ease factor
  let newInterval: number;
  let newEasinessFactor = easinessFactor;

  // Adjust ease factor based on performance
  newEasinessFactor += (difficulty - 1) * EASE_FACTOR_MODIFIER;
  newEasinessFactor = Math.max(MINIMUM_EASE_FACTOR, newEasinessFactor);

  // Determine the next interval based on difficulty
  if (difficulty === ReviewDifficulty.DIFFICULT) {
    // If difficult, reset interval to 1 day
    newInterval = 1 * 24 * 60 * 60 * 1000; // 1 day in milliseconds
  } else {
    if (reviewCount === 0) {
      // First time getting it right: 1 day
      newInterval = 1 * 24 * 60 * 60 * 1000;
    } else if (reviewCount === 1) {
      // Second time getting it right: 6 days
      newInterval = 6 * 24 * 60 * 60 * 1000;
    } else {
      // Subsequent times: multiply the previous interval by the ease factor
      newInterval = Math.round(interval * newEasinessFactor);
    }
  }

  return {
    ...card,
    lastReviewed: now,
    nextReview: now + newInterval,
    interval: newInterval,
    easinessFactor: newEasinessFactor,
    reviewCount: reviewCount + 1,
    correct,
    incorrect,
  };
}

/**
 * Sort flashcards by their due date (null dates come first)
 */
export function sortFlashcardsByDueDate(cards: Flashcard[]): Flashcard[] {
  return [...cards].sort((a, b) => {
    // Cards without a next review date come first
    if (!a.nextReview) return -1;
    if (!b.nextReview) return 1;

    // Otherwise sort by next review date
    return a.nextReview - b.nextReview;
  });
}

/**
 * Calculate mastery percentage based on review history
 */
export function calculateMasteryPercentage(card: Flashcard): number {
  const totalReviews = (card.correct || 0) + (card.incorrect || 0);
  if (totalReviews === 0) return 0;

  return Math.round(((card.correct || 0) / totalReviews) * 100);
}

/**
 * Implements a simplified SM-2 like algorithm for quiz questions.
 */
export function updateQuizQuestionSRS(
  question: QuizQuestion,
  difficulty: ReviewDifficulty
): QuizQuestion {
  const now = Date.now();

  // Ensure srs_fields are initialized if they are null/undefined
  // Supabase types use `string | null` for timestamps, `number | null` for numbers.
  // Defaulting to 0 or initial values if null.
  const srs_interval_ms = question.srs_interval || 0; // Assuming interval is stored in ms
  const srs_ease_factor = question.srs_ease_factor || INITIAL_EASE_FACTOR;
  const srs_repetitions = question.srs_repetitions || 0;

  let newIntervalMs: number;
  let newEasinessFactor = srs_ease_factor;

  if (difficulty === ReviewDifficulty.DIFFICULT) {
    newIntervalMs = 1 * 24 * 60 * 60 * 1000; // Reset to 1 day
    newEasinessFactor = Math.max(MINIMUM_EASE_FACTOR, newEasinessFactor - 0.2); // Penalize ease factor
  } else {
    // MEDIUM or EASY
    if (srs_repetitions === 0) {
      newIntervalMs = 1 * 24 * 60 * 60 * 1000; // 1 day
    } else if (srs_repetitions === 1) {
      newIntervalMs = 6 * 24 * 60 * 60 * 1000; // 6 days
    } else {
      newIntervalMs = Math.round(srs_interval_ms * newEasinessFactor);
    }
    // Adjust ease factor based on performance (SM-2 formula style)
    // q = 0 (difficult), 3 (medium), 5 (easy) in original SM-2.
    // Our difficulty: 0 (DIFFICULT), 1 (MEDIUM), 2 (EASY)
    // Map to SM-2 quality ratings: 0 -> 0 (not used here), 1 -> 3, 2 -> 5 (approx)
    const quality = difficulty === ReviewDifficulty.MEDIUM ? 3 : 5; // Simplified mapping for non-difficult
    newEasinessFactor =
      newEasinessFactor + (0.1 - (5 - quality) * (0.08 + (5 - quality) * 0.02));
  }

  newEasinessFactor = Math.max(MINIMUM_EASE_FACTOR, newEasinessFactor);

  const currentCorrectStreak = question.srs_correct_streak || 0;
  const newCorrectStreak =
    difficulty === ReviewDifficulty.DIFFICULT ? 0 : currentCorrectStreak + 1;

  return {
    ...question,
    last_reviewed_at: new Date(now).toISOString(),
    due_at: new Date(now + newIntervalMs).toISOString(),
    srs_interval: newIntervalMs,
    srs_ease_factor: newEasinessFactor,
    srs_repetitions: srs_repetitions + 1,
    srs_correct_streak: newCorrectStreak,
    srs_level:
      (question.srs_level || 0) +
      (difficulty === ReviewDifficulty.EASY
        ? 1
        : difficulty === ReviewDifficulty.MEDIUM
        ? 0
        : -1), // Simplistic level update
  };
}

/**
 * Sort quiz questions by their SRS due dates, prioritizing questions that are due for review.
 * Questions without due dates (never reviewed) come first, followed by overdue questions,
 * then questions due today, then future questions.
 */
export function sortQuizQuestionsByDueDate(
  questions: Tables<"quiz_questions">[]
): Tables<"quiz_questions">[] {
  const now = new Date();

  return [...questions].sort((a, b) => {
    // Questions without due dates (never reviewed) come first
    if (!a.due_at && !b.due_at) return 0;
    if (!a.due_at) return -1;
    if (!b.due_at) return 1;

    // Parse due dates
    const dueDateA = new Date(a.due_at);
    const dueDateB = new Date(b.due_at);

    // Both have due dates - sort by due date (earlier dates first)
    return dueDateA.getTime() - dueDateB.getTime();
  });
}

/**
 * Filter quiz questions to only include those that are due for review.
 * This includes questions that have never been reviewed (no due_at) and questions past their due date.
 */
export function getQuizQuestionsDueForReview(
  questions: Tables<"quiz_questions">[]
): Tables<"quiz_questions">[] {
  const now = new Date();

  return questions.filter((question) => {
    // Include questions that have never been reviewed
    if (!question.due_at) return true;

    // Include questions that are past their due date
    const dueDate = new Date(question.due_at);
    return dueDate <= now;
  });
}

/**
 * Get count of questions due for review
 */
export function getQuizQuestionsDueCount(
  questions: Tables<"quiz_questions">[]
): number {
  return getQuizQuestionsDueForReview(questions).length;
}
