/**
 * Environment Variable Validation Utilities
 * Provides comprehensive validation and debugging for environment variables
 */

export interface EnvironmentConfig {
  supabaseUrl: string;
  supabaseAnonKey: string;
  apiBaseUrl: string;
  nodeEnv: string;
  isDevelopment: boolean;
  isProduction: boolean;
}

/**
 * Validates and returns all required environment variables
 * Throws descriptive errors if any required variables are missing
 */
export function validateEnvironmentVariables(): EnvironmentConfig {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required environment variables
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
  
  // Optional environment variables with fallbacks
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || "http://localhost:5000/api";
  const nodeEnv = import.meta.env.NODE_ENV || "development";
  
  // Environment flags
  const isDevelopment = import.meta.env.DEV || nodeEnv === "development";
  const isProduction = import.meta.env.PROD || nodeEnv === "production";

  // Validate required variables
  if (!supabaseUrl) {
    errors.push("VITE_SUPABASE_URL is required but not set");
  }

  if (!supabaseAnonKey) {
    errors.push("VITE_SUPABASE_ANON_KEY is required but not set");
  }

  // Validate URL formats
  if (supabaseUrl && !isValidUrl(supabaseUrl)) {
    errors.push("VITE_SUPABASE_URL must be a valid URL");
  }

  if (apiBaseUrl && !isValidUrl(apiBaseUrl)) {
    warnings.push("VITE_API_BASE_URL should be a valid URL");
  }

  // Log validation results
  if (isDevelopment) {
    console.log("🔍 Environment Variable Validation Results:");
    console.log("✅ Required variables:", errors.length === 0 ? "All present" : `${errors.length} missing`);
    console.log("⚠️ Warnings:", warnings.length === 0 ? "None" : `${warnings.length} issues`);
    
    if (warnings.length > 0) {
      console.warn("⚠️ Environment Variable Warnings:");
      warnings.forEach(warning => console.warn(`  - ${warning}`));
    }
  }

  // Throw error if any required variables are missing
  if (errors.length > 0) {
    const errorMessage = [
      "❌ Environment Variable Validation Failed:",
      ...errors.map(error => `  - ${error}`),
      "",
      "📋 Troubleshooting Steps:",
      "1. Check if .env file exists in project root",
      "2. Verify all required VITE_ prefixed variables are set",
      "3. Restart the development server after making changes",
      "4. Ensure no typos in variable names",
      "",
      "📄 Required variables:",
      "  - VITE_SUPABASE_URL=https://your-project.supabase.co",
      "  - VITE_SUPABASE_ANON_KEY=your_anon_key",
    ].join("\n");

    throw new Error(errorMessage);
  }

  return {
    supabaseUrl,
    supabaseAnonKey,
    apiBaseUrl,
    nodeEnv,
    isDevelopment,
    isProduction,
  };
}

/**
 * Simple URL validation
 */
function isValidUrl(string: string): boolean {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

/**
 * Logs environment information for debugging
 */
export function logEnvironmentInfo(): void {
  if (import.meta.env.DEV) {
    console.log("🌍 Environment Information:");
    console.log("- Mode:", import.meta.env.MODE);
    console.log("- Development:", import.meta.env.DEV);
    console.log("- Production:", import.meta.env.PROD);
    console.log("- Node Environment:", import.meta.env.NODE_ENV);
    
    // Log available VITE_ variables (without sensitive values)
    const viteVars = Object.keys(import.meta.env)
      .filter(key => key.startsWith('VITE_'))
      .reduce((acc, key) => {
        // Hide sensitive values
        const value = key.includes('KEY') || key.includes('SECRET') 
          ? (import.meta.env[key] ? '***CONFIGURED***' : 'NOT_SET')
          : import.meta.env[key];
        acc[key] = value;
        return acc;
      }, {} as Record<string, any>);
    
    console.log("- VITE Variables:", viteVars);
  }
}

/**
 * Gets environment configuration with validation
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  return validateEnvironmentVariables();
}
