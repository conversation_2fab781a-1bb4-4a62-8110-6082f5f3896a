export interface AIProviderSettings {
  provider: string;
  baseUrl: string;
  apiKey: string;
  model: string;
}

export interface Flashcard {
  id: string; // Unique ID for the flashcard
  question: string;
  answer: string;
  // Add other fields as needed, e.g., deckId, srsData
}

export interface GenerateFlashcardsRequest {
  textContent: string;
  aiProviderSettings: AIProviderSettings;
}

export interface GenerateFlashcardsResponse {
  flashcards: Flashcard[];
} 