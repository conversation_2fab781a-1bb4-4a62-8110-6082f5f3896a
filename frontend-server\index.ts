import express from "express";
import path from "path";
import { fileURLToPath } from "url";
import cors from "cors";
import { createProxyMiddleware } from "http-proxy-middleware";

// Get current file's directory in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();

// Load environment variables only in development
if (process.env.NODE_ENV !== "production") {
  const { default: dotenv } = await import("dotenv");
  dotenv.config({ path: path.resolve(__dirname, "../.env") });
}

// Security headers middleware
app.use((req, res, next) => {
  // Security headers
  res.setHeader("X-Content-Type-Options", "nosniff");
  res.setHeader("X-Frame-Options", "DENY");
  res.setHeader("X-XSS-Protection", "1; mode=block");
  res.setHeader("Referrer-Policy", "strict-origin-when-cross-origin");
  const cspPolicy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://replit.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: https:; font-src 'self' data: https://fonts.gstatic.com; connect-src 'self' ws: wss: https://chewy-ai.replit.app/:5000 https:;";
  res.setHeader("Content-Security-Policy", cspPolicy);

  // Debug logging for CSP policy
  if (process.env.NODE_ENV === "production") {
    console.log("🔒 CSP Policy set:", cspPolicy.includes("fonts.googleapis.com") ? "✅ Google Fonts allowed" : "❌ Google Fonts blocked");
  }
  
  next();
});

// CORS configuration for API requests to backend
app.use(
  cors({
    origin: process.env.NODE_ENV === "production" 
      ? true 
      : ["https://chewy-ai.replit.app/:3000", "https://chewy-ai.replit.app/:5000"],
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);

// Parse JSON bodies
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: false }));

// API proxy to backend server
// Determine backend URL based on environment
let backendUrl: string;

if (process.env.BACKEND_URL) {
  // Use explicitly set backend URL (for deployment environments)
  backendUrl = process.env.BACKEND_URL;
} else if (process.env.NODE_ENV === "production") {
  // In production without explicit BACKEND_URL, assume same-host communication
  // Backend runs on PORT (default 5000), frontend runs on FRONTEND_PORT (default 3000)
  const backendPort = process.env.PORT || "5000";
  backendUrl = `http://localhost:${backendPort}`;
} else {
  // Development environment - backend typically on port 5000
  backendUrl = "http://localhost:5000";
}

console.log(`🔗 Proxying API requests to: ${backendUrl}`);
console.log(`📊 Environment: NODE_ENV=${process.env.NODE_ENV}, PORT=${process.env.PORT}, FRONTEND_PORT=${process.env.FRONTEND_PORT}`);

app.use('/api', createProxyMiddleware({
  target: backendUrl,
  changeOrigin: true,
  secure: false,
  timeout: 30000,
  proxyTimeout: 30000,
  onError: (err, req, res) => {
    console.error('❌ Proxy error:', err.message);
    console.error('🔍 Target URL:', backendUrl);
    console.error('🌐 Request URL:', req.url);
    console.error('📊 Environment details:', {
      NODE_ENV: process.env.NODE_ENV,
      PORT: process.env.PORT,
      FRONTEND_PORT: process.env.FRONTEND_PORT,
      BACKEND_URL: process.env.BACKEND_URL
    });
    res.status(500).json({
      error: 'Backend service unavailable',
      message: 'Unable to connect to API server',
      details: process.env.NODE_ENV === 'development' ? {
        target: backendUrl,
        error: err.message
      } : undefined
    });
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log(`[PROXY] ${req.method} ${req.url} -> ${backendUrl}${req.url}`);
  },
  onProxyRes: (proxyRes, req, res) => {
    console.log(`[PROXY] ${req.method} ${req.url} <- ${proxyRes.statusCode}`);
  }
}));

// Serve static files from the Vite build output
const distPath = path.resolve(__dirname, "../dist/public");
console.log(`Serving static files from: ${distPath}`);

// Static file serving with proper caching headers
app.use(express.static(distPath, {
  maxAge: process.env.NODE_ENV === "production" ? "1y" : "0",
  etag: true,
  lastModified: true,
  setHeaders: (res, filePath) => {
    // Cache static assets for a year in production
    if (process.env.NODE_ENV === "production") {
      if (filePath.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/)) {
        res.setHeader("Cache-Control", "public, max-age=31536000, immutable");
      } else if (filePath.endsWith("index.html")) {
        res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
      }
    }
  }
}));

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({ 
    status: "healthy", 
    service: "frontend-server",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || "development"
  });
});

// SPA fallback - serve index.html for all non-API routes
app.get("*", (req, res) => {
  // API routes are handled by the proxy middleware above
  // This fallback only handles frontend routes
  const indexPath = path.join(distPath, "index.html");
  res.sendFile(indexPath, (err) => {
    if (err) {
      console.error("Error serving index.html:", err);
      res.status(500).json({ error: "Failed to serve application" });
    }
  });
});

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error("Frontend server error:", err);
  res.status(500).json({ 
    error: "Internal server error",
    message: process.env.NODE_ENV === "development" ? err.message : "Something went wrong"
  });
});

// Function to check backend connectivity
async function checkBackendConnectivity(): Promise<boolean> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    const response = await fetch(`${backendUrl}/api/health`, {
      method: 'GET',
      signal: controller.signal
    });

    clearTimeout(timeoutId);
    return response.ok;
  } catch (error) {
    console.warn(`⚠️  Backend not immediately available at ${backendUrl}/api/health:`, error instanceof Error ? error.message : 'Unknown error');
    return false;
  }
}

// Start the frontend server
const port = parseInt(process.env.FRONTEND_PORT || "3000", 10);
const host = "0.0.0.0";

app.listen(port, host, async () => {
  console.log(`✅ Frontend server running on port ${port}`);
  console.log(`🌐 Frontend available at: https://chewy-ai.replit.app/:${port}`);
  console.log(`🔍 Frontend health check: https://chewy-ai.replit.app/:${port}/health`);
  console.log(`📁 Serving static files from: ${distPath}`);

  // Check backend connectivity (non-blocking)
  console.log(`🔍 Checking backend connectivity...`);
  const backendAvailable = await checkBackendConnectivity();
  if (backendAvailable) {
    console.log(`✅ Backend is reachable at ${backendUrl}`);
  } else {
    console.log(`⚠️  Backend not immediately available at ${backendUrl}`);
    console.log(`   This is normal if the backend is still starting up.`);
    console.log(`   API requests will be retried automatically.`);
  }
});

// Handle graceful shutdown
const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];
signals.forEach((signal) => {
  process.on(signal, () => {
    console.log(`Frontend server received ${signal}, shutting down gracefully...`);
    process.exit(0);
  });
});
