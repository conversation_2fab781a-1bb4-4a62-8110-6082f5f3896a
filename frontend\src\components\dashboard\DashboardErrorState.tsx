import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertTriangle, RefreshCw, Wifi, WifiOff } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface DashboardErrorStateProps {
  error: Error;
  onRetry: () => void;
  isRetrying?: boolean;
  showNetworkStatus?: boolean;
}

interface ErrorCardProps {
  title: string;
  icon: React.ReactElement;
  isError?: boolean;
}

const ErrorCard: React.FC<ErrorCardProps> = ({ title, icon, isError = true }) => {
  const cardClasses = isError 
    ? "bg-slate-800 border-red-500/50 text-red-400 shadow-lg"
    : "bg-slate-800 border-slate-700 text-slate-400 shadow-lg";
  
  const iconClasses = isError ? "h-5 w-5 text-red-400" : "h-5 w-5 text-slate-400";
  const iconContainerClasses = isError 
    ? "p-2 bg-red-500/20 rounded-lg flex items-center justify-center"
    : "p-2 bg-slate-600/20 rounded-lg flex items-center justify-center";

  return (
    <Card className={cardClasses}>
      <CardContent className="p-5 flex flex-col space-y-2">
        <div className="flex items-start justify-between w-full">
          <h3 className="text-base font-medium text-slate-300">{title}</h3>
          <div className={iconContainerClasses}>
            {React.cloneElement(icon, { className: iconClasses })}
          </div>
        </div>
        <p className="text-2xl font-bold text-red-400">Error</p>
        <p className="text-xs text-slate-400">Failed to load data</p>
      </CardContent>
    </Card>
  );
};

const DashboardErrorState: React.FC<DashboardErrorStateProps> = ({
  error,
  onRetry,
  isRetrying = false,
  showNetworkStatus = true,
}) => {
  const { toast } = useToast();
  const [isOnline, setIsOnline] = React.useState(navigator.onLine);

  React.useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleRetry = () => {
    if (!isOnline) {
      toast({
        title: "No Internet Connection",
        description: "Please check your internet connection and try again.",
        variant: "destructive",
      });
      return;
    }

    onRetry();
    toast({
      title: "Retrying...",
      description: "Attempting to reload dashboard data.",
    });
  };

  const isNetworkError = error.message.toLowerCase().includes('fetch') || 
                        error.message.toLowerCase().includes('network') ||
                        error.message.toLowerCase().includes('connection');

  const errorCards = [
    { title: "Documents", icon: <AlertTriangle /> },
    { title: "Total Flashcards", icon: <AlertTriangle /> },
    { title: "Total Quizzes", icon: <AlertTriangle /> },
    { title: "Completions", icon: <AlertTriangle /> },
  ];

  return (
    <div className="space-y-4">
      {/* Error Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {errorCards.map((card) => (
          <ErrorCard
            key={card.title}
            title={card.title}
            icon={card.icon}
            isError={true}
          />
        ))}
      </div>

      {/* Error Details and Actions */}
      <Card className="bg-slate-800 border-red-500/50">
        <CardContent className="p-6">
          <div className="flex items-start space-x-4">
            <div className="p-3 bg-red-500/20 rounded-lg">
              <AlertTriangle className="h-6 w-6 text-red-400" />
            </div>
            <div className="flex-1 space-y-3">
              <div>
                <h3 className="text-lg font-semibold text-red-400 mb-1">
                  Dashboard Data Unavailable
                </h3>
                <p className="text-slate-300 text-sm">
                  {isNetworkError 
                    ? "Unable to connect to the server. This might be due to network issues or server maintenance."
                    : "There was an error loading your dashboard statistics. Some services may be temporarily unavailable."
                  }
                </p>
              </div>

              {showNetworkStatus && (
                <div className="flex items-center space-x-2 text-sm">
                  {isOnline ? (
                    <>
                      <Wifi className="h-4 w-4 text-green-400" />
                      <span className="text-green-400">Connected</span>
                    </>
                  ) : (
                    <>
                      <WifiOff className="h-4 w-4 text-red-400" />
                      <span className="text-red-400">No Internet Connection</span>
                    </>
                  )}
                </div>
              )}

              <div className="flex items-center space-x-3">
                <Button
                  onClick={handleRetry}
                  disabled={isRetrying || !isOnline}
                  className="flex items-center space-x-2"
                  variant="default"
                >
                  <RefreshCw className={`h-4 w-4 ${isRetrying ? 'animate-spin' : ''}`} />
                  <span>{isRetrying ? 'Retrying...' : 'Try Again'}</span>
                </Button>

                {process.env.NODE_ENV === 'development' && (
                  <details className="text-xs">
                    <summary className="text-slate-400 cursor-pointer hover:text-slate-300">
                      Error Details (Development)
                    </summary>
                    <div className="mt-2 p-3 bg-slate-900 rounded border border-slate-600">
                      <pre className="text-xs text-red-300 overflow-auto whitespace-pre-wrap">
                        {error.message}
                        {error.stack && `\n\nStack trace:\n${error.stack}`}
                      </pre>
                    </div>
                  </details>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DashboardErrorState;
