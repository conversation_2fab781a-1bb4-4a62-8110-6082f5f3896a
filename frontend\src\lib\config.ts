// API Configuration for different environments
export const API_BASE_URL = (() => {
  // In development, always use the proxy to avoid CORS issues
  if (import.meta.env.NODE_ENV === "development") {
    return "/api";
  }
  
  // In production, use the configured URL or default
  return import.meta.env.VITE_API_BASE_URL || "https://chewy-ai.replit.app/api";
})();

// Environment detection
export const isDevelopment = import.meta.env.NODE_ENV === "development";
export const isProduction = import.meta.env.NODE_ENV === "production";

// Backend URL for direct access (used for health checks and debugging)
export const BACKEND_URL = (() => {
  if (isDevelopment) {
    return "http://localhost:5000";
  }
  return import.meta.env.VITE_BACKEND_URL || "https://chewy-ai.replit.app";
})();

// Log configuration in development
if (isDevelopment) {
  console.log("🔧 API Configuration:", {
    API_BASE_URL,
    BACKEND_URL,
    environment: isDevelopment ? "development" : "production",
    viteEnv: import.meta.env.VITE_API_BASE_URL ? "configured" : "using default",
    nodeEnv: import.meta.env.NODE_ENV,
  });
}