# ChewyAI Troubleshooting Guide

## 🚨 Common Issues and Solutions

### Flashcard System Issues

#### Issue: "Deck Not Found" Error in Flashcard Review
**Symptoms:**
- Clicking "Review" on flashcard sets causes application reload
- All flashcard sets show "0 Cards" after error
- 500 Internal Server Error from `/api/flashcard-sets/{id}/due`

**Root Cause:**
Database schema mismatch - the flashcard review system was trying to query non-existent SRS columns like `due_at`, `next_review_date`, etc.

**Solution (Fixed in January 2025):**
1. Updated `/api/flashcard-sets/{id}/due` endpoint to work with current Supabase schema
2. Fixed flashcard count calculation in backend
3. Updated frontend to properly handle card counts from backend response

**Prevention:**
- Always verify database schema matches API queries
- Test flashcard review functionality after schema changes
- Monitor backend logs for database column errors

#### Issue: Flashcard Sets Show "0 Cards"
**Symptoms:**
- Newly generated flashcard sets display incorrect card counts
- Card counts reset to 0 after page reload

**Root Cause:**
Frontend was not properly using the `card_count` field from backend response.

**Solution:**
1. Backend now includes `card_count` in flashcard set responses
2. Frontend updated to use `card_count` instead of hardcoded 0
3. Proper mapping between Supabase response and client-side data structures

### Development Server Issues

#### Issue: Port Conflicts (EADDRINUSE)
**Symptoms:**
- Server fails to start with "port already in use" errors
- Multiple npm processes running simultaneously

**Solution:**
```bash
# Kill all existing processes
pkill -f "npm run dev"
pkill -f "tsx server" 
pkill -f "vite"

# Wait for ports to be released
sleep 3

# Restart development server
npm run dev
```

#### Issue: Backend Changes Not Applied
**Symptoms:**
- Code changes in server files not reflected in API responses
- Old error messages persist after fixes

**Solution:**
1. Ensure development server restarts after backend changes
2. Check terminal for compilation errors
3. Verify hot reload is working for server files
4. Force restart if hot reload fails

### Database Issues

#### Issue: Supabase Connection Errors
**Symptoms:**
- Authentication failures
- Database query timeouts
- "Invalid JWT" errors

**Solution:**
1. Verify environment variables are set correctly
2. Check Supabase project status
3. Ensure JWT tokens are valid and not expired
4. Verify RLS policies allow user access

### AI Integration Issues

#### Issue: AI Generation Failures
**Symptoms:**
- Flashcard generation returns empty results
- API key authentication errors
- Model not found errors

**Solution:**
1. Verify AI provider credentials are stored correctly
2. Check API key permissions and quotas
3. Ensure model names are correct for the provider
4. Monitor backend logs for detailed error messages

## 🔧 Debugging Tools

### Backend Debugging
```bash
# View server logs
npm run dev

# Check specific API endpoint
curl -H "Authorization: Bearer <token>" http://localhost:5000/api/health
```

### Frontend Debugging
- Open browser developer tools
- Check Network tab for API request/response details
- Monitor Console for JavaScript errors
- Use React Developer Tools for component state

### Database Debugging
- Use Supabase dashboard to inspect tables
- Check RLS policies and permissions
- Verify data integrity and relationships

## 📋 Health Check Checklist

When troubleshooting issues, verify:

- [ ] Development server is running on correct ports (3000, 5000)
- [ ] Environment variables are loaded correctly
- [ ] Supabase connection is working
- [ ] User authentication is functioning
- [ ] API endpoints return expected status codes
- [ ] Database tables have correct schema
- [ ] Frontend can communicate with backend
- [ ] AI provider credentials are configured

## 🆘 Getting Help

If issues persist:
1. Check the full error logs in terminal
2. Verify the issue against this troubleshooting guide
3. Test with a minimal reproduction case
4. Document the exact steps that cause the issue
5. Include relevant error messages and logs

## 📝 Recent Fixes (January 2025)

### Flashcard System Overhaul
- **Fixed**: Database schema mismatch causing 500 errors
- **Fixed**: Card count display issues
- **Fixed**: Flashcard review functionality
- **Improved**: Error handling and logging
- **Updated**: API documentation with current endpoints
