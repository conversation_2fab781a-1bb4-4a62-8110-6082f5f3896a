#!/bin/bash
# Simple development startup script for ChewyAI
# This script starts both frontend and backend development servers
# Compatible with G<PERSON> Bash on Windows

# Don't exit on error initially - we'll handle errors manually
# set -e

# Colors for output (check if terminal supports colors)
if [ -t 1 ]; then
    RED='\033[0;31m'
    GREEN='\033[0;32m'
    YELLOW='\033[1;33m'
    BLUE='\033[0;34m'
    NC='\033[0m' # No Color
else
    RED=''
    GREEN=''
    YELLOW=''
    BLUE=''
    NC=''
fi

# Function to print colored output
print_status() {
    echo -e "${BLUE}[DEV]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]:-$0}" )" && pwd )"
cd "$SCRIPT_DIR"

print_status "Starting ChewyAI development environment..."

# Check if .env file exists, create from example if not
if [ ! -f ".env" ]; then
    if [ -f "env.example" ]; then
        print_warning ".env file not found, creating from env.example..."
        cp env.example .env
        print_success "Created .env file from env.example"
        print_warning "Please review and update .env file with your specific configuration if needed"
    else
        print_error "No .env or env.example file found!"
        exit 1
    fi
fi

# Check if node_modules exists in root
if [ ! -d "node_modules" ]; then
    print_warning "Root node_modules not found, installing dependencies..."
    npm install
fi

# Check if frontend node_modules exists
if [ ! -d "frontend/node_modules" ]; then
    print_warning "Frontend node_modules not found, installing dependencies..."
    cd frontend && npm install && cd ..
fi

# Check if backend node_modules exists
if [ ! -d "backend/node_modules" ]; then
    print_warning "Backend node_modules not found, installing dependencies..."
    cd backend && npm install && cd ..
fi

# Load environment variables with Windows compatibility
load_env_file() {
    local env_file="$1"
    print_debug "Attempting to load environment file: $env_file"

    if [ ! -f "$env_file" ]; then
        print_warning "Environment file $env_file not found"
        return 1
    fi

    print_debug "Environment file exists, checking format..."

    # Check if file has Windows line endings and convert if needed
    if command -v dos2unix >/dev/null 2>&1; then
        print_debug "Converting line endings with dos2unix..."
        dos2unix "$env_file" 2>/dev/null || true
    elif command -v sed >/dev/null 2>&1; then
        print_debug "Converting line endings with sed..."
        sed -i 's/\r$//' "$env_file" 2>/dev/null || true
    fi

    print_debug "Processing environment variables..."

    # Method 1: Try using the dot command (more portable than source)
    if (set -a; . "$env_file"; set +a) 2>/dev/null; then
        print_debug "Successfully loaded using dot command"
        set -a
        . "$env_file"
        set +a
        return 0
    fi

    print_debug "Dot command failed, trying manual parsing..."

    # Method 2: Manual parsing for problematic files
    while IFS= read -r line || [ -n "$line" ]; do
        # Skip empty lines and comments
        case "$line" in
            ''|'#'*) continue ;;
        esac

        # Extract key=value pairs
        if echo "$line" | grep -q '='; then
            key=$(echo "$line" | cut -d'=' -f1)
            value=$(echo "$line" | cut -d'=' -f2-)

            # Remove quotes if present
            value=$(echo "$value" | sed 's/^["'\'']//' | sed 's/["'\'']$//')

            # Export the variable
            export "$key"="$value"
            print_debug "Exported: $key"
        fi
    done < "$env_file"

    print_debug "Manual parsing completed"
    return 0
}

if [ -f ".env" ]; then
    print_status "Loading environment variables from .env..."
    if load_env_file ".env"; then
        print_success "Environment variables loaded successfully"
    else
        print_warning "Failed to load .env file, continuing with defaults..."
    fi
else
    print_warning "No .env file found, using default values"
fi

# Set development environment variables with fallbacks
export NODE_ENV=${NODE_ENV:-development}
export FRONTEND_PORT=${FRONTEND_PORT:-3000}
export BACKEND_PORT=${BACKEND_PORT:-5000}
export PORT=${PORT:-$BACKEND_PORT}

print_status "Environment configuration:"
echo "  - NODE_ENV: $NODE_ENV"
echo "  - Frontend Port: $FRONTEND_PORT"
echo "  - Backend Port: $BACKEND_PORT"
echo "  - Frontend URL: http://localhost:$FRONTEND_PORT"
echo "  - Backend API URL: http://localhost:$BACKEND_PORT"

# Verify npm and node are available
print_status "Checking system requirements..."
if ! command -v node >/dev/null 2>&1; then
    print_error "Node.js is not installed or not in PATH"
    exit 1
fi

if ! command -v npm >/dev/null 2>&1; then
    print_error "npm is not installed or not in PATH"
    exit 1
fi

print_success "Node.js $(node --version) and npm $(npm --version) are available"

echo ""
print_status "Starting development servers..."
echo "  - Frontend (React + Vite): http://localhost:$FRONTEND_PORT"
echo "  - Backend (Express + tsx): http://localhost:$BACKEND_PORT"
echo ""
print_warning "Press Ctrl+C to stop all servers"
echo ""

# Start both servers using the existing npm script
# This uses concurrently to run both frontend and backend
print_status "Executing: npm run dev"

# Add error handling for npm run dev
if npm run dev; then
    print_success "Development servers started successfully"
else
    print_error "Failed to start development servers"
    print_status "Trying alternative startup method..."

    # Alternative: Start servers individually
    print_status "Starting backend server..."
    cd backend && npm run dev &
    BACKEND_PID=$!

    sleep 2

    print_status "Starting frontend server..."
    cd ../frontend && npm run dev &
    FRONTEND_PID=$!

    # Wait for both processes
    wait $BACKEND_PID $FRONTEND_PID
fi
