import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import Spinner from "@/components/ui/Spinner";

interface QuizGenerationOptions {
  numberOfQuestions: number;
  questionTypes: string[];
  customPrompt: string;
}

interface QuizGenerationPopupProps {
  trigger: React.ReactNode;
  onGenerate: (options: QuizGenerationOptions) => Promise<void>;
  isGenerating: boolean;
  disabled?: boolean;
  maxQuestions?: number;
}

const QUESTION_TYPE_OPTIONS = [
  { id: "multiple_choice", label: "Multiple Choice" },
  { id: "select_all_that_apply", label: "Select All That Apply" },
  { id: "true_false", label: "True/False" },
  { id: "short_answer", label: "Short Answer" },
];

export const QuizGenerationPopup: React.FC<QuizGenerationPopupProps> = ({
  trigger,
  onGenerate,
  isGenerating,
  disabled = false,
  maxQuestions = 20,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [options, setOptions] = useState<QuizGenerationOptions>({
    numberOfQuestions: 10,
    questionTypes: ["multiple_choice"],
    customPrompt: "",
  });

  const handleQuestionTypeChange = (typeId: string) => {
    setOptions((prev) => ({
      ...prev,
      questionTypes: prev.questionTypes.includes(typeId)
        ? prev.questionTypes.filter((qt) => qt !== typeId)
        : [...prev.questionTypes, typeId],
    }));
  };

  const handleGenerate = async () => {
    if (options.questionTypes.length === 0) {
      return; // Validation handled by UI
    }

    try {
      await onGenerate(options);
      setIsOpen(false);
    } catch (error) {
      // Error handling is done by parent component
      console.error("Quiz generation failed:", error);
    }
  };

  const isValidOptions = options.questionTypes.length > 0 && options.numberOfQuestions > 0;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild disabled={disabled}>
        {trigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px] bg-slate-800 border-slate-700">
        <DialogHeader>
          <DialogTitle className="text-slate-100">Generate Quiz Options</DialogTitle>
          <DialogDescription className="text-slate-400">
            Configure quiz generation settings including question types, number of questions, and custom instructions.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Number of Questions */}
          <div className="space-y-2">
            <Label htmlFor="numberOfQuestions" className="text-slate-200">
              Number of Questions
            </Label>
            <Input
              id="numberOfQuestions"
              type="number"
              min="1"
              max={maxQuestions}
              value={options.numberOfQuestions}
              onChange={(e) =>
                setOptions((prev) => ({
                  ...prev,
                  numberOfQuestions: Math.max(1, Math.min(maxQuestions, parseInt(e.target.value) || 1)),
                }))
              }
              className="bg-slate-700 border-slate-600 text-slate-100"
            />
          </div>

          {/* Question Types */}
          <div className="space-y-3">
            <Label className="text-slate-200">Question Types</Label>
            <div className="space-y-2">
              {QUESTION_TYPE_OPTIONS.map((option) => (
                <div key={option.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={option.id}
                    checked={options.questionTypes.includes(option.id)}
                    onCheckedChange={() => handleQuestionTypeChange(option.id)}
                    className="border-slate-500 data-[state=checked]:bg-purple-600 data-[state=checked]:border-purple-600"
                  />
                  <Label
                    htmlFor={option.id}
                    className="text-sm text-slate-300 cursor-pointer"
                  >
                    {option.label}
                  </Label>
                </div>
              ))}
            </div>
            {options.questionTypes.length === 0 && (
              <p className="text-sm text-red-400">
                Please select at least one question type.
              </p>
            )}
          </div>

          {/* Custom Prompt */}
          <div className="space-y-2">
            <Label htmlFor="customPrompt" className="text-slate-200">
              Custom Prompt (Optional)
            </Label>
            <Textarea
              id="customPrompt"
              placeholder="Add any specific instructions for the AI to follow when generating the quiz..."
              value={options.customPrompt}
              onChange={(e) =>
                setOptions((prev) => ({
                  ...prev,
                  customPrompt: e.target.value,
                }))
              }
              className="bg-slate-700 border-slate-600 text-slate-100 min-h-[80px]"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-slate-700">
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isGenerating}
            className="border-slate-600 text-slate-300 hover:bg-slate-700"
          >
            Cancel
          </Button>
          <Button
            onClick={handleGenerate}
            disabled={!isValidOptions || isGenerating}
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >            {isGenerating ? (
              <div className="flex items-center">
                <Spinner size="sm" />
                <span className="ml-2">Generating...</span>
              </div>
            ) : (
              "Generate Quiz"
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
