import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { supabaseConfig } from '../config';
import * as schema from '../../shared/schema'; // Assuming your schema is here

// Declare the db variable at the top level
let db: any;

// Only attempt database connection if password is provided
if (!supabaseConfig.dbPassword || supabaseConfig.dbPassword === 'YOUR_POSTGRES_PASSWORD_PLACEHOLDER_FROM_ENV_OR_DIRECTLY_SET' || supabaseConfig.dbPassword === 'your-postgres-password') {
  console.warn('\n**********************************************************************************************************************');
  console.warn('* WARNING: Database password not found or is a placeholder.                                                        *');
  console.warn('* Please ensure SUPABASE_DB_PASSWORD is set correctly in your .env file.                                         *');
  console.warn('* Skipping direct database connection - using Supabase client instead.                                           *');
  console.warn('**********************************************************************************************************************\n');
  
  // Assign null to db to prevent crashes
  db = null;
} else {
  try {
    // Extract hostname from Supabase URL
    const supabaseUrl = new URL(supabaseConfig.url);
    const hostname = supabaseUrl.hostname;
    
    // Construct the connection string
    const connectionString = `postgres://postgres:${supabaseConfig.dbPassword}@${hostname}:5432/postgres`;
    
    const client = postgres(connectionString, { prepare: false });
    db = drizzle(client, { schema });
    
    console.log('drizzle.ts: Database client initialized successfully.');
  } catch (error) {
    console.error('drizzle.ts: Failed to initialize database client:', error);
    console.error('Please check your SUPABASE_URL and SUPABASE_DB_PASSWORD configuration.');
    db = null;
  }
}

export { db };