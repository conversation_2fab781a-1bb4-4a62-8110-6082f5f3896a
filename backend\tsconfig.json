{
  "compilerOptions": {
    "target": "ES2022",
<<<<<<< HEAD:backend/tsconfig.json
    "lib": ["ES2022"],
=======
>>>>>>> 366134829ca7366f1440a0df99db6a8eb2de9639:server/tsconfig.json
    "module": "ESNext",
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "allowJs": true,
    "strict": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
<<<<<<< HEAD:backend/tsconfig.json
    "types": ["node"]
=======
    "types": [
      "node",
      "jest"
    ]
>>>>>>> 366134829ca7366f1440a0df99db6a8eb2de9639:server/tsconfig.json
  },
  "include": [
    "**/*.ts",
    "**/*.js"
  ],
  "exclude": [
    "node_modules",
    "dist"
  ]
}
