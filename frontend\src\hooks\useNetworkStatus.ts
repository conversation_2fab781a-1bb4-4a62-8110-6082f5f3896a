import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';

interface NetworkStatus {
  isOnline: boolean;
  isSlowConnection: boolean;
  connectionType: string | null;
}

export const useNetworkStatus = () => {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isOnline: navigator.onLine,
    isSlowConnection: false,
    connectionType: null,
  });
  const { toast } = useToast();

  useEffect(() => {
    const updateNetworkStatus = () => {
      const connection = (navigator as any).connection || 
                        (navigator as any).mozConnection || 
                        (navigator as any).webkitConnection;
      
      setNetworkStatus({
        isOnline: navigator.onLine,
        isSlowConnection: connection ? 
          ['slow-2g', '2g'].includes(connection.effectiveType) : false,
        connectionType: connection?.effectiveType || null,
      });
    };

    const handleOnline = () => {
      updateNetworkStatus();
      toast({
        title: "Connection Restored",
        description: "You're back online. Dashboard will refresh automatically.",
        duration: 3000,
      });
    };

    const handleOffline = () => {
      updateNetworkStatus();
      toast({
        title: "Connection Lost",
        description: "You're offline. Some features may not work properly.",
        variant: "destructive",
        duration: 5000,
      });
    };

    const handleConnectionChange = () => {
      updateNetworkStatus();
    };

    // Initial setup
    updateNetworkStatus();

    // Event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection;
    
    if (connection) {
      connection.addEventListener('change', handleConnectionChange);
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      if (connection) {
        connection.removeEventListener('change', handleConnectionChange);
      }
    };
  }, [toast]);

  return networkStatus;
};

export default useNetworkStatus;
