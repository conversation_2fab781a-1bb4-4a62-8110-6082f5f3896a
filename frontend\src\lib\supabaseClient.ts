/// <reference types="vite/client" />
import { createClient } from "@supabase/supabase-js";
import { Database } from "../types/supabase"; // Assuming you will generate this type

/**
 * Environment Variable Validation and Loading
 * This function provides comprehensive debugging information for environment variable issues
 */
function validateAndLoadEnvironmentVariables() {
  // Debug: Log all available environment variables (filtered for security)
  console.log("🔍 Environment Variable Debug Information:");
  console.log("- NODE_ENV:", import.meta.env.NODE_ENV);
  console.log("- MODE:", import.meta.env.MODE);
  console.log("- DEV:", import.meta.env.DEV);
  console.log("- PROD:", import.meta.env.PROD);

  // Check for Supabase environment variables
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

  console.log("🔍 Supabase Environment Variables:");
  console.log("- VITE_SUPABASE_URL:", supabaseUrl ? "✓ Found" : "❌ Missing");
  console.log(
    "- VITE_SUPABASE_ANON_KEY:",
    supabaseAnonKey ? "✓ Found" : "❌ Missing"
  );

  // Provide detailed error information if variables are missing
  if (!supabaseUrl) {
    console.error("❌ VITE_SUPABASE_URL is not set!");
    console.error("📋 Troubleshooting steps:");
    console.error("1. Check if .env file exists in project root");
    console.error("2. Verify VITE_SUPABASE_URL is set in .env file");
    console.error(
      "3. Restart the development server after adding environment variables"
    );
    console.error("4. Ensure the variable name starts with 'VITE_' prefix");

    throw new Error(
      "VITE_SUPABASE_URL is not set. Please check your .env file and ensure it contains VITE_SUPABASE_URL=your_supabase_url"
    );
  }

  if (!supabaseAnonKey) {
    console.error("❌ VITE_SUPABASE_ANON_KEY is not set!");
    console.error("📋 Troubleshooting steps:");
    console.error("1. Check if .env file exists in project root");
    console.error("2. Verify VITE_SUPABASE_ANON_KEY is set in .env file");
    console.error(
      "3. Restart the development server after adding environment variables"
    );
    console.error("4. Ensure the variable name starts with 'VITE_' prefix");

    throw new Error(
      "VITE_SUPABASE_ANON_KEY is not set. Please check your .env file and ensure it contains VITE_SUPABASE_ANON_KEY=your_supabase_anon_key"
    );
  }

  return { supabaseUrl, supabaseAnonKey };
}

// Validate and load environment variables
const { supabaseUrl, supabaseAnonKey } = validateAndLoadEnvironmentVariables();

// Create Supabase client
console.log("✅ Supabase client initialization successful");
console.log("✅ URL:", supabaseUrl);
console.log("✅ Anon Key:", supabaseAnonKey ? "Configured" : "Missing");

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);
