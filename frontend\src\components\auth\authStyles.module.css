.darkInput {
  background-color: #1e293b !important; /* slate-800 */
  color: #e2e8f0 !important; /* slate-200 */
  border-color: #475569 !important; /* slate-600 */
  box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.25) !important;
}

.darkInput:focus {
  border-color: #a855f7 !important; /* purple-500 */
  box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.25), 0 0 0 2px #a855f720 !important;
  outline: none !important;
}

.darkInput::placeholder {
  color: #94a3b8 !important; /* slate-400 */
}

.signInButton {
  background-color: #9333ea !important; /* purple-600 */
  color: white !important;
  border: none !important;
  transition: background-color 0.2s ease !important;
}

.signInButton:hover:not(:disabled) {
  background-color: #7e22ce !important; /* purple-700 */
}

.signInButton:disabled {
  opacity: 0.5 !important;
}

.formContainer {
  background-color: #1e293b !important; /* slate-800 */
  border: 1px solid #334155 !important; /* slate-700 */
}

.formTitle {
  color: #c084fc !important; /* purple-400 */
}

.formLabel {
  color: #d8b4fe !important; /* purple-300 */
}
