import React, { useEffect } from "react";
import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./lib/queryClient";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ApiProvider } from "@/contexts/ApiContext";
import { AuthProvider } from "@/hooks/useAuth";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { QuizSettingsProvider } from "@/contexts/QuizSettingsContext";
import { ErrorBoundary } from "@/components/layout/ErrorBoundary";

const GlobalEscapeHandler: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  useEffect(() => {
    const handleGlobalEsc = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        try {
          // Attempt to find and close Radix-based dialogs/popovers
          // This is a best-effort approach. Specific components might need their own handling if this isn't sufficient.
          const openDialog = document.querySelector(
            '[role="dialog"][data-state="open"], [role="alertdialog"][data-state="open"]'
          );

          if (openDialog && document.contains(openDialog)) {
            // Try to find a specific close button. Shadcn typically uses X icon with sr-only "Close"
            const closeButton =
              openDialog.querySelector('button[aria-label="Close"]') || // Common for Dialog, Sheet
              openDialog.querySelector('button[aria-label="Cancel"]'); // Common for AlertDialog

            if (
              closeButton instanceof HTMLElement &&
              document.contains(closeButton)
            ) {
              closeButton.click();
              event.preventDefault(); // Prevent other ESC handlers if we found one
              return;
            }
          }
        } catch (error) {
          console.warn("Error in GlobalEscapeHandler:", error);
        }
      }
    };

    document.addEventListener("keydown", handleGlobalEsc, { passive: false });
    return () => {
      try {
        document.removeEventListener("keydown", handleGlobalEsc);
      } catch (error) {
        console.warn("Error removing global escape handler:", error);
      }
    };
  }, []);

  return <>{children}</>;
};

// Prevent multiple root creation by using a global variable
const rootElement = document.getElementById("root")!;

// Store root instance globally to prevent multiple creation
declare global {
  interface Window {
    __REACT_ROOT__?: ReturnType<typeof createRoot>;
  }
}

let root: ReturnType<typeof createRoot>;

// Check if we already have a root instance stored globally
if (!window.__REACT_ROOT__) {
  root = createRoot(rootElement);
  window.__REACT_ROOT__ = root;
  console.log("React root created");
} else {
  root = window.__REACT_ROOT__;
  console.log("Using existing React root");
}

root.render(
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <GlobalEscapeHandler>
          <QuizSettingsProvider>
            <TooltipProvider>
              <ApiProvider>
                <AuthProvider>
                  <Toaster />
                  <App />
                </AuthProvider>
              </ApiProvider>
            </TooltipProvider>
          </QuizSettingsProvider>
        </GlobalEscapeHandler>
      </ThemeProvider>
    </QueryClientProvider>
  </ErrorBoundary>
);
