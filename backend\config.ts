// Production-ready configuration using environment variables
// Fallback values should only be used for development

<<<<<<< HEAD:backend/config.ts
/**
 * Supabase Configuration
 * Loads configuration from environment variables with fallbacks
 */
export const supabaseConfig = {
  url: process.env.SUPABASE_URL || "https://hrdjfukhzbzksqaupqie.supabase.co",
  serviceRoleKey:
    process.env.SUPABASE_SERVICE_ROLE_KEY ||
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhyZGpmdWtoemJ6a3NxYXVwcWllIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzI0Mjk0OSwiZXhwIjoyMDYyODE4OTQ5fQ.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0",
  dbPassword:
    process.env.SUPABASE_DB_PASSWORD ||
    process.env.VITE_DATABASE_PASSWORD ||
    "",
};

// Validation and logging
=======
export const supabaseConfig = {
  url: process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL || "https://hrdjfukhzbzksqaupqie.supabase.co",
  serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhyZGpmdWtoemJ6a3NxYXVwcWllIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzI0Mjk0OSwiZXhwIjoyMDYyODE4OTQ5fQ.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0",
  anonKey: process.env.SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhyZGpmdWtoemJ6a3NxYXVwcWllIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyNDI5NDksImV4cCI6MjA2MjgxODk0OX0.0qS0Nh1aV4GjZYniANoUZKDDcuCcQxzTm-DnTeZsNlQ",
  dbPassword: process.env.SUPABASE_DB_PASSWORD || "",
};

// Validate required environment variables in production
if (process.env.NODE_ENV === 'production') {
  const requiredEnvVars = ['SUPABASE_URL', 'SUPABASE_SERVICE_ROLE_KEY'];
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables for production:', missingVars);
    console.error('Please set these environment variables before starting the server.');
    process.exit(1);
  }
}

// Validation - only check essential config
if (!supabaseConfig.url || !supabaseConfig.serviceRoleKey || !supabaseConfig.anonKey) {
  console.error(
    "❌ Error: Supabase URL, Service Role Key, and Anonymous Key must be configured."
  );
  console.error("Current config:", {
    url: supabaseConfig.url ? "✓ Set" : "✗ Missing",
    serviceRoleKey: supabaseConfig.serviceRoleKey ? "✓ Set" : "✗ Missing",
    anonKey: supabaseConfig.anonKey ? "✓ Set" : "✗ Missing",
  });
  process.exit(1);
}

// Validate URL format
try {
  new URL(supabaseConfig.url);
} catch (error) {
  console.error("❌ Error: Invalid Supabase URL format:", supabaseConfig.url);
  console.error("Please ensure SUPABASE_URL is a valid URL (e.g., https://your-project.supabase.co)");
  process.exit(1);
}

// Log configuration status
>>>>>>> 366134829ca7366f1440a0df99db6a8eb2de9639:server/config.ts
console.log("✓ Supabase configuration loaded successfully");
console.log("✓ URL:", supabaseConfig.url ? "Configured" : "Missing");
console.log(
  "✓ Service Role Key:",
  supabaseConfig.serviceRoleKey ? "Configured" : "Missing"
);
console.log(
  "✓ Anonymous Key:",
  supabaseConfig.anonKey ? "Configured" : "Missing"
);
console.log(
  "✓ Database Password:",
  supabaseConfig.dbPassword
    ? "Optional - using service role key authentication"
    : "Optional - using service role key authentication"
<<<<<<< HEAD:backend/config.ts
);

// Validation - only check essential config
if (!supabaseConfig.url || !supabaseConfig.serviceRoleKey) {
  console.error("❌ Critical Supabase configuration missing!");
  console.error(
    "Please ensure SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in your .env file"
  );
  console.error("Current config:", {
    url: supabaseConfig.url ? "✓ Set" : "✗ Missing",
    serviceRoleKey: supabaseConfig.serviceRoleKey ? "✓ Set" : "✗ Missing",
  });
  throw new Error("Missing required Supabase configuration");
}
=======
);
>>>>>>> 366134829ca7366f1440a0df99db6a8eb2de9639:server/config.ts
