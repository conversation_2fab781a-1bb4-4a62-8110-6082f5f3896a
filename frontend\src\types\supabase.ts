export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  public: {
    Tables: {
      flashcard_sets: {
        Row: {
          created_at: string | null;
          description: string | null;
          id: string;
          name: string;
          study_document_id: string | null;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          created_at?: string | null;
          description?: string | null;
          id?: string;
          name: string;
          study_document_id?: string | null;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          created_at?: string | null;
          description?: string | null;
          id?: string;
          name?: string;
          study_document_id?: string | null;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "flashcard_sets_study_document_id_fkey";
            columns: ["study_document_id"];
            isOneToOne: false;
            referencedRelation: "study_documents";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "flashcard_sets_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      flashcards: {
        Row: {
          back_text: string;
          created_at: string | null;
          front_text: string;
          id: string;
          set_id: string;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          back_text: string;
          created_at?: string | null;
          front_text: string;
          id?: string;
          set_id: string;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          back_text?: string;
          created_at?: string | null;
          front_text?: string;
          id?: string;
          set_id?: string;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "flashcards_set_id_fkey";
            columns: ["set_id"];
            isOneToOne: false;
            referencedRelation: "flashcard_sets";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "flashcards_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      profiles: {
        Row: {
          avatar_url: string | null;
          email: string | null;
          full_name: string | null;
          id: string;
          updated_at: string | null;
          username: string | null;
        };
        Insert: {
          avatar_url?: string | null;
          email?: string | null;
          full_name?: string | null;
          id: string;
          updated_at?: string | null;
          username?: string | null;
        };
        Update: {
          avatar_url?: string | null;
          email?: string | null;
          full_name?: string | null;
          id?: string;
          updated_at?: string | null;
          username?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "profiles_id_fkey";
            columns: ["id"];
            isOneToOne: true;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      quiz_questions: {
        Row: {
          correct_answer: string | null;
          created_at: string | null;
          due_at: string | null;
          explanation: string | null;
          id: string;
          last_reviewed_at: string | null;
          options: Json | null;
          question_text: string;
          quiz_id: string;
          srs_correct_streak: number | null;
          srs_ease_factor: number | null;
          srs_interval: number | null;
          srs_level: number | null;
          srs_repetitions: number | null;
          type: Database["public"]["Enums"]["question_type"];
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          correct_answer?: string | null;
          created_at?: string | null;
          due_at?: string | null;
          explanation?: string | null;
          id?: string;
          last_reviewed_at?: string | null;
          options?: Json | null;
          question_text: string;
          quiz_id: string;
          srs_correct_streak?: number | null;
          srs_ease_factor?: number | null;
          srs_interval?: number | null;
          srs_level?: number | null;
          srs_repetitions?: number | null;
          type: Database["public"]["Enums"]["question_type"];
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          correct_answer?: string | null;
          created_at?: string | null;
          due_at?: string | null;
          explanation?: string | null;
          id?: string;
          last_reviewed_at?: string | null;
          options?: Json | null;
          question_text?: string;
          quiz_id?: string;
          srs_correct_streak?: number | null;
          srs_ease_factor?: number | null;
          srs_interval?: number | null;
          srs_level?: number | null;
          srs_repetitions?: number | null;
          type?: Database["public"]["Enums"]["question_type"];
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "quiz_questions_quiz_id_fkey";
            columns: ["quiz_id"];
            isOneToOne: false;
            referencedRelation: "quizzes";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "quiz_questions_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      quizzes: {
        Row: {
          created_at: string | null;
          description: string | null;
          id: string;
          name: string;
          study_document_id: string | null;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          created_at?: string | null;
          description?: string | null;
          id?: string;
          name: string;
          study_document_id?: string | null;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          created_at?: string | null;
          description?: string | null;
          id?: string;
          name?: string;
          study_document_id?: string | null;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "quizzes_study_document_id_fkey";
            columns: ["study_document_id"];
            isOneToOne: false;
            referencedRelation: "study_documents";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "quizzes_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      study_documents: {
        Row: {
          content_type: string | null;
          created_at: string | null;
          extracted_text_path: string | null;
          extracted_text_summary: string | null;
          file_name: string;
          file_path: string;
          id: string;
          size_bytes: number | null;
          status: string | null;
          storage_object_id: string | null;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          content_type?: string | null;
          created_at?: string | null;
          extracted_text_path?: string | null;
          extracted_text_summary?: string | null;
          file_name: string;
          file_path: string;
          id?: string;
          size_bytes?: number | null;
          status?: string | null;
          storage_object_id?: string | null;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          content_type?: string | null;
          created_at?: string | null;
          extracted_text_path?: string | null;
          extracted_text_summary?: string | null;
          file_name?: string;
          file_path?: string;
          id?: string;
          size_bytes?: number | null;
          status?: string | null;
          storage_object_id?: string | null;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "study_documents_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }        ];
      };
      user_completions: {
        Row: {
          completed_at: string;
          completion_type: string;
          correct_answers: number | null;
          created_at: string;
          flashcard_set_id: string | null;
          id: string;
          metadata: Json | null;
          questions_answered: number | null;
          quiz_id: string | null;
          score: number | null;
          time_spent_minutes: number | null;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          completed_at?: string;
          completion_type: string;
          correct_answers?: number | null;
          created_at?: string;
          flashcard_set_id?: string | null;
          id?: string;
          metadata?: Json | null;
          questions_answered?: number | null;
          quiz_id?: string | null;
          score?: number | null;
          time_spent_minutes?: number | null;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          completed_at?: string;
          completion_type?: string;
          correct_answers?: number | null;
          created_at?: string;
          flashcard_set_id?: string | null;
          id?: string;
          metadata?: Json | null;
          questions_answered?: number | null;
          quiz_id?: string | null;
          score?: number | null;
          time_spent_minutes?: number | null;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "user_completions_flashcard_set_id_fkey";
            columns: ["flashcard_set_id"];
            isOneToOne: false;
            referencedRelation: "flashcard_sets";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "user_completions_quiz_id_fkey";
            columns: ["quiz_id"];
            isOneToOne: false;
            referencedRelation: "quizzes";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "user_completions_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      handle_new_user: {
        Args: Record<PropertyKey, never>;
        Returns: Record<PropertyKey, never>;
      };
      handle_user_email_update: {
        Args: Record<PropertyKey, never>;
        Returns: Record<PropertyKey, never>;
      };
      handle_user_metadata_update: {
        Args: Record<PropertyKey, never>;
        Returns: Record<PropertyKey, never>;
      };
      set_flashcard_user_id: {
        Args: Record<PropertyKey, never>;
        Returns: Record<PropertyKey, never>;
      };
      set_quiz_question_user_id: {
        Args: Record<PropertyKey, never>;
        Returns: Record<PropertyKey, never>;
      };
    };
    Enums: {
      question_type:
        | "multiple_choice"
        | "true_false"
        | "short_answer"
        | "fill_in_the_blank"
        | "select_all_that_apply";
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type PublicSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
      PublicSchema["Views"])
  ? (PublicSchema["Tables"] &
      PublicSchema["Views"])[PublicTableNameOrOptions] extends {
      Row: infer R;
    }
    ? R
    : never
  : never;

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
  ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
      Insert: infer I;
    }
    ? I
    : never
  : never;

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
  ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
      Update: infer U;
    }
    ? U
    : never
  : never;

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
  ? PublicSchema["Enums"][PublicEnumNameOrOptions]
  : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
  ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never;

export const Constants = {
  public: {
    Enums: {
      question_type: [
        "multiple_choice",
        "true_false",
        "short_answer",
        "fill_in_the_blank",
        "select_all_that_apply",
      ],
    },
  },
} as const;
