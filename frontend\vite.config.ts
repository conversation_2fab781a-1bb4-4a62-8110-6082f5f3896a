import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import { fileURLToPath } from "url";
import runtimeErrorOverlay from "@replit/vite-plugin-runtime-error-modal";

// Get current file's directory in ES modules
const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default defineConfig({
  // Configure Vite to load environment variables from the root directory
  // This ensures that the .env file in the project root is loaded
  envDir: path.resolve(__dirname, ".."),

  plugins: [
    react(),
    // Disable Replit plugins that might interfere with HMR
    ...(process.env.NODE_ENV !== "production" && !process.env.REPL_ID
      ? [runtimeErrorOverlay()]
      : []),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"),
      "@shared": path.resolve(__dirname, "shared"),
      "@assets": path.resolve(__dirname, "attached_assets"),
    },
  },
  build: {
    outDir: path.resolve(__dirname, "dist"),
    emptyOutDir: true,
    // Production optimizations
    minify: 'esbuild',
    sourcemap: process.env.NODE_ENV !== 'production',
    rollupOptions: {
      output: {
        // Optimize chunk splitting for better caching
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu'],
          router: ['wouter'],
          query: ['@tanstack/react-query'],
        },
        // Add hash to filenames for cache busting
        chunkFileNames: 'assets/[name]-[hash].js',
        entryFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    },
    // Increase chunk size warning limit for production
    chunkSizeWarningLimit: 1000,
  },
  server: {
    host: "0.0.0.0", // Allow external connections
    port: 3000,
    proxy: {
      "/api": {
        target: process.env.VITE_API_URL || "http://localhost:5000",
        changeOrigin: true,
        secure: false,
        timeout: 15000, // Increased timeout to 15 seconds
        configure: (proxy, _options) => {
          proxy.on('error', (err, req, res) => {
            console.log('❌ Proxy error:', err.message);
            console.log('   Request:', req.method, req.url);
            console.log('   Target: http://localhost:5000');
            
            // Check if backend is running
            if (err.code === 'ECONNREFUSED') {
              console.log('💡 Backend server appears to be down. Please ensure it\'s running on port 5000');
            }
            
            // Send a proper error response instead of letting it hang
            if (!res.headersSent) {
              res.writeHead(503, { 
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
              });
              res.end(JSON.stringify({ 
                error: 'Backend server unavailable', 
                message: 'Please ensure the backend server is running on port 5000',
                code: err.code || 'PROXY_ERROR'
              }));
            }
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('📤 Proxying request:', req.method, req.url, '-> http://localhost:5000');
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('📥 Received response:', proxyRes.statusCode, req.url);
          });
        },
      },
    },
    // Add the allowedHosts configuration here
    allowedHosts: [
      "304e5062-821d-42b4-8bf3-52cee6a14ffd-00-35plnljz3wwcd.worf.replit.dev",
      "chewy-ai.replit.app",
      "localhost",
      "127.0.0.1",
    ],
    // Handle port conflicts
    strictPort: false,
    // Fix HMR WebSocket connection issues
    hmr: process.env.REPL_ID ? {
      port: 24678,
      host: "0.0.0.0",
      clientPort: 443,
    } : {
      port: 24679,
      host: "localhost",
      clientPort: 3000,
    },
    // Additional configuration for Replit
    ...(process.env.REPL_ID && {
      watch: null,
    }),
  },
});