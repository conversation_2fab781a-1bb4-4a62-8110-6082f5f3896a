import { users, type User, type InsertUser } from "@shared/schema";
import axios from "axios";

// modify the interface with any CRUD methods
// you might need

// AI Provider settings interface
interface AISettings {
  provider: string;
  baseUrl: string;
  apiKey: string;
  model: string;
}

// Flashcard interface
interface Flashcard {
  question: string;
  answer: string;
}

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  generateFlashcards(content: string, documentName: string, count: number, aiSettings: AISettings): Promise<Flashcard[]>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  currentId: number;

  constructor() {
    this.users = new Map();
    this.currentId = 1;
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }
  
  async generateFlashcards(
    content: string, 
    documentName: string, 
    count: number = 10,
    aiSettings: AISettings
  ): Promise<Flashcard[]> {
    try {
      // Prepare the prompt for the AI
      const prompt = this.createFlashcardPrompt(content, count, documentName);
      
      // Make request to AI provider (using OpenRouter API format)
      const response = await axios.post(
        `${aiSettings.baseUrl}/chat/completions`,
        {
          model: aiSettings.model,
          messages: [
            {
              role: "system",
              content: "You are an intelligent study assistant that creates high-quality flashcards. Your task is to analyze text and create clear, concise question-answer pairs that test key concepts. Always respond in a structured format that can be easily parsed as JSON."
            },
            { role: "user", content: prompt }
          ],
          response_format: { type: "json_object" }
        },
        {
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${aiSettings.apiKey}`
          }
        }
      );
      
      // Parse the response
      const responseContent = response.data.choices[0].message.content;
      const parsedContent = JSON.parse(responseContent);
      
      // Validate and extract flashcards
      if (Array.isArray(parsedContent.flashcards)) {
        return parsedContent.flashcards.map((card: any) => ({
          question: card.question,
          answer: card.answer
        }));
      } else {
        throw new Error("Invalid response format from AI provider");
      }
    } catch (error) {
      console.error("Error generating flashcards:", error);
      
      if (axios.isAxiosError(error)) {
        const statusCode = error.response?.status;
        const responseData = error.response?.data;
        
        throw new Error(
          `AI provider error (${statusCode}): ${
            responseData?.error?.message || error.message
          }`
        );
      }
      
      throw new Error(`Failed to generate flashcards: ${(error as Error).message}`);
    }
  }
  
  private createFlashcardPrompt(content: string, count: number, documentName: string): string {
    return `
      I need you to create ${count} flashcards based on the following content from "${documentName}". 
      Each flashcard should have a question on one side and the answer on the other side.
      
      Focus on key concepts, definitions, and important relationships.
      Make sure the questions are clear and specific.
      Keep answers concise but complete.
      
      Here's the content to analyze:
      
      ${content}
      
      Respond with a JSON object in the following format:
      {
        "flashcards": [
          {
            "question": "Question text here?",
            "answer": "Answer text here."
          },
          ...more flashcards
        ]
      }
      
      Only return the JSON object, nothing else.
    `;
  }
}

export const storage = new MemStorage();
