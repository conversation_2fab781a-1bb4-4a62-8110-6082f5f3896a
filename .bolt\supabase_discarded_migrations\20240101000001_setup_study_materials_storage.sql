-- Create storage bucket for study materials if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('study_materials', 'study_materials', false)
ON CONFLICT (id) DO NOTHING;

-- Enable RLS on storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Policy for users to upload their own files
CREATE POLICY "Users can upload their own files" ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'study_materials' AND
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy for users to view their own files
CREATE POLICY "Users can view their own files" ON storage.objects FOR SELECT
USING (
  bucket_id = 'study_materials' AND
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy for users to update their own files
CREATE POLICY "Users can update their own files" ON storage.objects FOR UPDATE
USING (
  bucket_id = 'study_materials' AND
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy for users to delete their own files
CREATE POLICY "Users can delete their own files" ON storage.objects FOR DELETE
USING (
  bucket_id = 'study_materials' AND
  auth.uid()::text = (storage.foldername(name))[1]
); 