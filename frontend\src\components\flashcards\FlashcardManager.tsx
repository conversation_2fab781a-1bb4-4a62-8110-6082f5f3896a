import React, { useEffect, useState, FormEvent } from "react";
// Removed direct Supabase import - using backend API endpoints
import { useAuth } from "../../hooks/useAuth";
import { Tables, TablesInsert } from "../../types/supabase";
import MarkdownRenderer from "../document/MarkdownRenderer";

type Flashcard = Tables<"flashcards">;
type FlashcardInsert = TablesInsert<"flashcards">;

interface FlashcardManagerProps {
  selectedSetId: string;
  selectedSetName: string;
  onClose: () => void; // To go back to set list or close manager
}

export const FlashcardManager: React.FC<FlashcardManagerProps> = ({
  selectedSetId,
  selectedSetName,
  onClose,
}) => {
  const { user } = useAuth();
  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // For adding/editing flashcards
  const [isEditing, setIsEditing] = useState<Flashcard | null>(null);
  const [frontText, setFrontText] = useState("");
  const [backText, setBackText] = useState("");
  const [formLoading, setFormLoading] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFlashcards = async () => {
      if (!user || !selectedSetId) return;
      setLoading(true);
      setError(null);
      try {
        const token = localStorage.getItem('auth_token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        const response = await fetch(`/api/flashcard-sets/${selectedSetId}/flashcards`, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        setFlashcards(data || []);
      } catch (err: any) {
        setError(err.message || "Failed to fetch flashcards.");
      } finally {
        setLoading(false);
      }
    };

    fetchFlashcards();
    // Real-time updates removed - using manual refresh
  }, [user, selectedSetId]);

  const handleFormSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!frontText.trim() || !backText.trim()) {
      setFormError("Front and back text cannot be empty.");
      return;
    }
    setFormLoading(true);
    setFormError(null);

    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      if (isEditing) {
        // Update existing flashcard
        const response = await fetch(`/api/flashcard-sets/${selectedSetId}/flashcards/${isEditing.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({
            front_text: frontText,
            back_text: backText,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        const updatedFlashcard = await response.json();
        setFlashcards(
          flashcards.map((fc) =>
            fc.id === isEditing.id ? updatedFlashcard : fc
          )
        );
        alert("Flashcard updated!");
      } else {
        // Add new flashcard
        const response = await fetch(`/api/flashcard-sets/${selectedSetId}/flashcards`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({
            front_text: frontText,
            back_text: backText,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        const newFlashcard = await response.json();
        setFlashcards([...flashcards, newFlashcard]);
        alert("Flashcard added!");
      }
      // Reset form
      setIsEditing(null);
      setFrontText("");
      setBackText("");
    } catch (err: any) {
      setFormError(err.message || "Failed to save flashcard.");
    } finally {
      setFormLoading(false);
    }
  };

  const startEdit = (flashcard: Flashcard) => {
    setIsEditing(flashcard);
    setFrontText(flashcard.front_text);
    setBackText(flashcard.back_text);
    setFormError(null);
  };

  const cancelEdit = () => {
    setIsEditing(null);
    setFrontText("");
    setBackText("");
    setFormError(null);
  };

  const handleDeleteFlashcard = async (flashcardId: string) => {
    if (!window.confirm("Are you sure you want to delete this flashcard?"))
      return;
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`/api/flashcard-sets/${selectedSetId}/flashcards/${flashcardId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      setFlashcards(flashcards.filter((fc) => fc.id !== flashcardId));
      alert("Flashcard deleted.");
    } catch (err: any) {
      alert(`Error deleting flashcard: ${err.message}`);
    }
  };

  if (loading)
    return (
      <p className="text-slate-400 p-4">
        Loading flashcards for set: {selectedSetName}...
      </p>
    );
  if (error) return <p className="text-red-400 p-4">Error: {error}</p>;

  return (
    <div className="mt-5 p-4 bg-slate-800 border border-slate-700 shadow-md rounded-lg text-slate-200">
      <div className="flex justify-between items-center mb-4">
        <h4 className="text-xl font-semibold text-purple-400">
          Managing Flashcards in "{selectedSetName}"
        </h4>
        <button
          onClick={onClose}
          className="text-sm text-purple-400 hover:text-purple-300 hover:underline"
        >
          Back to Sets
        </button>
      </div>

      {/* Form for Adding/Editing Flashcards */}
      <form
        onSubmit={handleFormSubmit}
        className="mb-6 p-4 border border-slate-700 rounded-md bg-slate-700/50 space-y-4"
      >
        <h5 className="text-md font-semibold text-slate-100">
          {isEditing ? "Edit Flashcard" : "Add New Flashcard"}
        </h5>
        {formError && <p className="text-red-400 text-sm">{formError}</p>}
        <div>
          <label
            htmlFor="frontText"
            className="block text-sm font-medium text-slate-300 mb-1"
          >
            Front Text
          </label>
          <textarea
            id="frontText"
            value={frontText}
            onChange={(e) => setFrontText(e.target.value)}
            required
            rows={3}
            className="mt-1 block w-full px-3 py-2 border border-slate-600 bg-slate-700 text-slate-100 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
          />
        </div>
        <div>
          <label
            htmlFor="backText"
            className="block text-sm font-medium text-slate-300 mb-1"
          >
            Back Text
          </label>
          <textarea
            id="backText"
            value={backText}
            onChange={(e) => setBackText(e.target.value)}
            required
            rows={3}
            className="mt-1 block w-full px-3 py-2 border border-slate-600 bg-slate-700 text-slate-100 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
          />
        </div>
        <div className="flex items-center space-x-3 pt-2">
          <button
            type="submit"
            disabled={formLoading}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-700 focus:ring-purple-500 disabled:opacity-50 disabled:bg-slate-500"
          >
            {formLoading
              ? isEditing
                ? "Saving..."
                : "Adding..."
              : isEditing
              ? "Save Changes"
              : "Add Flashcard"}
          </button>
          {isEditing && (
            <button
              type="button"
              onClick={cancelEdit}
              className="px-4 py-2 border border-slate-600 rounded-md shadow-sm text-sm font-medium text-slate-300 bg-slate-700 hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-700 focus:ring-purple-500"
            >
              Cancel Edit
            </button>
          )}
        </div>
      </form>

      {/* List of Flashcards in the Set */}
      {flashcards.length === 0 ? (
        <p className="text-slate-300">
          No flashcards in this set yet. Add some!
        </p>
      ) : (
        <ul className="space-y-3">
          {flashcards.map((fc) => (
            <li
              key={fc.id}
              className="p-3 border border-slate-700 rounded-md hover:bg-slate-700/60 text-slate-200"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-2">
                <div>
                  <strong className="text-slate-300">Front:</strong>
                  <div className="bg-slate-700 p-2 rounded-md mt-1">
                    <MarkdownRenderer
                      content={fc.front_text}
                      className="text-sm"
                    />
                  </div>
                </div>
                <div>
                  <strong className="text-slate-300">Back:</strong>
                  <div className="bg-slate-700 p-2 rounded-md mt-1">
                    <MarkdownRenderer
                      content={fc.back_text}
                      className="text-sm"
                    />
                  </div>
                </div>
              </div>
              <div className="flex space-x-2 mt-2">
                <button
                  onClick={() => startEdit(fc)}
                  className="text-xs border border-purple-500 text-purple-400 hover:bg-purple-500/20 hover:text-purple-300 py-1 px-2 rounded transition-colors"
                >
                  Edit
                </button>
                <button
                  onClick={() => handleDeleteFlashcard(fc.id)}
                  className="text-xs border border-red-500 text-red-400 hover:bg-red-500/20 hover:text-red-300 py-1 px-2 rounded transition-colors"
                >
                  Delete
                </button>
              </div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};
