# Security Migration Plan: Backend-Only Supabase Architecture

**Objective:** Remove all direct Supabase integrations from React.js frontend and route all operations through Express.js backend for proper security isolation.

## Migration Steps

### Phase 1: Remove Direct Supabase Client (Immediate - HIGH PRIORITY)

#### 1.1 Remove Supabase Client File
```bash
# This file must be deleted entirely
rm /client/src/lib/supabaseClient.ts
```

#### 1.2 Update Environment Configuration
**File:** `/client/.env.example`
```bash
# REMOVE these lines:
# VITE_SUPABASE_URL=
# VITE_SUPABASE_ANON_KEY=

# ADD these lines instead:
VITE_API_BASE_URL=http://localhost:5000/api
```

#### 1.3 Remove Supabase Package Dependency
```bash
# Update package.json to remove @supabase/supabase-js from client dependencies
# Move it to server-only dependencies if not already there
```

### Phase 2: Convert Database Operations to API Calls

#### 2.1 Replace useDocuments Hook
**File:** `/client/src/hooks/useDocuments.tsx`

**Current (VULNERABLE):**
```typescript
const { data, error } = await supabase
  .from("study_documents")
  .select("*")
  .eq("user_id", user.id)
```

**Secure Replacement:**
```typescript
export const useDocuments = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ["study-documents", user?.id],
    queryFn: async (): Promise<StudyDocument[]> => {
      if (!user) return [];

      const response = await fetch('/api/documents', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch documents');
      }

      return response.json();
    },
    enabled: !!user,
  });
};
```

#### 2.2 Update Dashboard Quiz Creation
**File:** `/client/src/pages/DashboardPage.tsx`

**Replace:**
```typescript
const { data, error } = await supabase
  .from("quizzes")
  .insert({
    user_id: user.id,
    name: quizName,
    description: quizDescription,
    study_document_id: document.id,
  })
```

**With:**
```typescript
const response = await fetch('/api/quizzes', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
  },
  body: JSON.stringify({
    name: quizName,
    description: quizDescription,
    study_document_id: document.id,
  }),
});

if (!response.ok) {
  throw new Error('Failed to create quiz');
}

const data = await response.json();
```

#### 2.3 Update Storage Operations
**File:** `/client/src/lib/storage.ts`

**Remove all Supabase direct queries and replace with API calls:**
```typescript
// Replace exportQuizzesAsJSON function
export async function exportQuizzesAsJSON(): Promise<string> {
  const response = await fetch('/api/quizzes/export?format=json', {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('Failed to export quizzes');
  }

  return response.text();
}

// Replace exportQuizzesAsCSV function
export async function exportQuizzesAsCSV(): Promise<string> {
  const response = await fetch('/api/quizzes/export?format=csv', {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('Failed to export quizzes');
  }

  return response.text();
}
```

### Phase 3: Remove Realtime Subscriptions

#### 3.1 Update QuizQuestionManager Component
**File:** `/client/src/components/quiz/QuizQuestionManager.tsx`

**Remove:**
```typescript
const channel = supabase
  .channel(`quiz_questions_in_${selectedQuizId}`)
  .on("postgres_changes", {...})
  .subscribe();

return () => {
  supabase.removeChannel(channel);
};
```

**Replace with polling:**
```typescript
useEffect(() => {
  fetchQuestions();

  // Poll for updates every 5 seconds instead of realtime
  const interval = setInterval(() => {
    fetchQuestions();
  }, 5000);

  return () => {
    clearInterval(interval);
  };
}, [user, selectedQuizId]);
```

#### 3.2 Update FlashcardManager Component
**File:** `/client/src/components/flashcards/FlashcardManager.tsx`

**Apply same polling pattern as above**

#### 3.3 Update FlashcardSetList Component
**File:** `/client/src/components/flashcards/FlashcardSetList.tsx`

**Apply same polling pattern as above**

### Phase 4: Update Authentication Flow

#### 4.1 Remove Direct Auth Session Access
**Files:**
- `/client/src/lib/file-parser.ts`
- `/client/src/lib/api/quizApi.ts`

**Remove:**
```typescript
const { data: { session } } = await supabase.auth.getSession();
```

**Replace with:**
```typescript
const token = localStorage.getItem('auth_token');
if (!token) {
  throw new Error('No authentication token available');
}
```

#### 4.2 Update API Helper Functions
**File:** `/client/src/lib/api.ts`

**Remove all instances of:**
```typescript
const { data: { session } } = await supabase.auth.getSession();
```

**The getAuthHeaders function is already correct - continue using it**

### Phase 5: Backend API Endpoints (Ensure These Exist)

#### 5.1 Required Backend Endpoints

The following API endpoints must exist on the backend:

```
GET    /api/documents                    - List user documents
POST   /api/documents                    - Create document
DELETE /api/documents/:id               - Delete document

GET    /api/quizzes                      - List user quizzes  
POST   /api/quizzes                      - Create quiz
GET    /api/quizzes/:id                  - Get quiz details
DELETE /api/quizzes/:id                 - Delete quiz
GET    /api/quizzes/export              - Export quizzes

GET    /api/flashcard-sets              - List flashcard sets
POST   /api/flashcard-sets              - Create flashcard set
GET    /api/flashcard-sets/:id          - Get flashcard set
DELETE /api/flashcard-sets/:id          - Delete flashcard set

GET    /api/flashcards                  - List flashcards
POST   /api/flashcards                  - Create flashcard
PUT    /api/flashcards/:id              - Update flashcard
DELETE /api/flashcards/:id              - Delete flashcard

POST   /api/auth/login                  - User login
POST   /api/auth/signup                 - User signup
POST   /api/auth/logout                 - User logout
GET    /api/auth/session               - Check session
```

### Phase 6: Environment Variables Cleanup

#### 6.1 Frontend Environment Variables
**File:** `/client/.env`
```bash
# REMOVE (these expose credentials):
# VITE_SUPABASE_URL=
# VITE_SUPABASE_ANON_KEY=

# KEEP (safe for frontend):
VITE_API_BASE_URL=http://localhost:5000/api
```

#### 6.2 Backend Environment Variables
**File:** `/server/.env` (backend only)
```bash
# KEEP (server-side only):
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
SUPABASE_ANON_KEY=your_anon_key

# Other backend configs
DATABASE_URL=your_database_url
JWT_SECRET=your_jwt_secret
```

### Phase 7: Update Import Statements

#### 7.1 Remove Supabase Imports from Frontend Files

**Find and remove these imports:**
```typescript
import { supabase } from "@/lib/supabaseClient";
import { createClient } from "@supabase/supabase-js";
```

**Update type imports to use shared types:**
```typescript
// Change from:
import { Tables } from "../types/supabase";

// To:
import { StudyDocument, Quiz, Flashcard } from "@shared/types";
```

### Phase 8: Security Validation

#### 8.1 Verification Checklist

- [ ] No `VITE_SUPABASE_*` environment variables in frontend
- [ ] No direct Supabase client instantiation in frontend code
- [ ] No direct database queries from frontend components
- [ ] No realtime subscriptions from frontend
- [ ] All database operations go through backend API
- [ ] Authentication tokens stored securely
- [ ] API endpoints have proper authorization middleware
- [ ] Rate limiting implemented on backend APIs

#### 8.2 Security Testing

```bash
# 1. Build frontend and check for exposed credentials
npm run build:client
grep -r "SUPABASE" dist/public/

# 2. Check frontend bundle for Supabase client code
grep -r "createClient\|@supabase" dist/public/

# 3. Verify no environment variables are exposed
curl http://localhost:3000 | grep -i "supabase\|VITE_"
```

### Phase 9: Monitoring and Maintenance

#### 9.1 Add Security Headers
```typescript
// In Express.js backend
app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  next();
});
```

#### 9.2 Implement API Rate Limiting
```typescript
import rateLimit from 'express-rate-limit';

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
});

app.use('/api/', limiter);
```

## Implementation Timeline

| Phase | Task | Duration | Priority |
|-------|------|----------|----------|
| 1 | Remove Supabase client | 1-2 hours | CRITICAL |
| 2 | Convert database operations | 4-6 hours | HIGH |
| 3 | Remove realtime subscriptions | 2-3 hours | MEDIUM |
| 4 | Update auth flow | 2-3 hours | HIGH |
| 5 | Verify backend APIs | 1-2 hours | HIGH |
| 6 | Environment cleanup | 1 hour | CRITICAL |
| 7 | Update imports | 1-2 hours | MEDIUM |
| 8 | Security validation | 2-3 hours | CRITICAL |

**Total Estimated Time:** 14-22 hours

## Risk Mitigation

1. **Backup current codebase** before starting migration
2. **Test each phase thoroughly** before proceeding to next
3. **Maintain API compatibility** during migration
4. **Monitor error rates** during deployment
5. **Have rollback plan** ready

## Success Criteria

✅ **Zero direct Supabase client usage in frontend**  
✅ **No exposed VITE_SUPABASE_* environment variables**  
✅ **All database operations via backend API**  
✅ **Proper authentication middleware on all endpoints**  
✅ **Security testing passes with no vulnerabilities**  

---

**Migration Plan Created By:** GitHub Copilot  
**Date:** June 2, 2025  
**Classification:** CONFIDENTIAL  
**Implementation Required:** IMMEDIATE
