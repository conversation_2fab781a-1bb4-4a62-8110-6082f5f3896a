import { useQuery } from "@tanstack/react-query";
import { useAuth } from "./useAuth";
import { Tables } from "@/types/supabase";
import { API_BASE_URL } from "@/lib/config";

type StudyDocument = Tables<"study_documents">;

export const useDocuments = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ["study-documents", user?.id],
    queryFn: async (): Promise<StudyDocument[]> => {
      if (!user) return [];

      console.log("useDocuments: Fetching documents for user:", user.id);

      const response = await fetch(`${API_BASE_URL}/documents`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("Error fetching documents:", errorData);
        throw new Error(errorData.error || 'Failed to fetch documents');
      }

      const data = await response.json();
      console.log("useDocuments: Fetched documents:", data);
      return data || [];
    },
    enabled: !!user,
  });
};
