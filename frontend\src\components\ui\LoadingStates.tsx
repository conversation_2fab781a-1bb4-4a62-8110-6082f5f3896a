import React from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardHeader } from "@/components/ui/card";

export const DashboardOverviewSkeleton: React.FC = () => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
    {Array.from({ length: 4 }).map((_, i) => (
      <Card key={i} className="bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Skeleton className="h-4 w-20 bg-slate-700" />
              <Skeleton className="h-8 w-16 bg-slate-700" />
            </div>
            <Skeleton className="h-8 w-8 rounded bg-slate-700" />
          </div>
        </CardContent>
      </Card>
    ))}
  </div>
);

export const FlashcardListSkeleton: React.FC = () => (
  <div className="space-y-4">
    {Array.from({ length: 5 }).map((_, i) => (
      <Card key={i} className="bg-slate-800 border-slate-700">
        <CardHeader>
          <div className="flex items-center justify-between">
            <Skeleton className="h-5 w-32 bg-slate-700" />
            <Skeleton className="h-4 w-16 bg-slate-700" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Skeleton className="h-4 w-full bg-slate-700" />
            <Skeleton className="h-4 w-3/4 bg-slate-700" />
          </div>
        </CardContent>
      </Card>
    ))}
  </div>
);

export const DocumentListSkeleton: React.FC = () => (
  <div className="space-y-3">
    {Array.from({ length: 4 }).map((_, i) => (
      <div
        key={i}
        className="flex items-center space-x-3 p-3 border border-slate-700 rounded-lg bg-slate-800"
      >
        <Skeleton className="h-8 w-8 rounded bg-slate-700" />
        <div className="flex-1 space-y-1">
          <Skeleton className="h-4 w-48 bg-slate-700" />
          <Skeleton className="h-3 w-24 bg-slate-700" />
        </div>
        <Skeleton className="h-8 w-20 bg-slate-700" />
      </div>
    ))}
  </div>
);

export const QuizListSkeleton: React.FC = () => (
  <div className="space-y-4">
    {Array.from({ length: 3 }).map((_, i) => (
      <Card key={i} className="bg-slate-800 border-slate-700">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Skeleton className="h-5 w-40 bg-slate-700" />
              <Skeleton className="h-4 w-32 bg-slate-700" />
            </div>
            <Skeleton className="h-8 w-24 bg-slate-700" />
          </div>
        </CardHeader>
      </Card>
    ))}
  </div>
);

export const FlashcardViewerSkeleton: React.FC = () => (
  <Card className="bg-slate-800 border-slate-700 min-h-[300px]">
    <CardContent className="p-8">
      <div className="text-center space-y-4">
        <Skeleton className="h-6 w-3/4 mx-auto bg-slate-700" />
        <Skeleton className="h-4 w-1/2 mx-auto bg-slate-700" />
        <Skeleton className="h-4 w-2/3 mx-auto bg-slate-700" />
        <div className="flex justify-center space-x-2 mt-8">
          <Skeleton className="h-10 w-20 bg-slate-700" />
          <Skeleton className="h-10 w-20 bg-slate-700" />
          <Skeleton className="h-10 w-20 bg-slate-700" />
        </div>
      </div>
    </CardContent>
  </Card>
);

export const AIGenerationSkeleton: React.FC = () => (
  <Card className="bg-slate-800 border-slate-700">
    <CardHeader>
      <Skeleton className="h-6 w-48 bg-slate-700" />
    </CardHeader>
    <CardContent className="space-y-4">
      <Skeleton className="h-4 w-full bg-slate-700" />
      <Skeleton className="h-4 w-3/4 bg-slate-700" />
      <div className="flex items-center space-x-2">
        <Skeleton className="h-4 w-4 rounded-full bg-slate-700" />
        <Skeleton className="h-4 w-32 bg-slate-700" />
      </div>
    </CardContent>
  </Card>
);

interface GenericSkeletonProps {
  rows?: number;
  className?: string;
}

export const GenericSkeleton: React.FC<GenericSkeletonProps> = ({
  rows = 3,
  className = "",
}) => (
  <div className={`space-y-3 ${className}`}>
    {Array.from({ length: rows }).map((_, i) => (
      <Skeleton key={i} className="h-4 w-full bg-slate-700" />
    ))}
  </div>
);

export const TableSkeleton: React.FC<{ rows?: number; cols?: number }> = ({
  rows = 5,
  cols = 4,
}) => (
  <div className="space-y-3">
    <div
      className="grid gap-3"
      style={{ gridTemplateColumns: `repeat(${cols}, 1fr)` }}
    >
      {Array.from({ length: cols }).map((_, i) => (
        <Skeleton key={i} className="h-5 bg-slate-600" />
      ))}
    </div>
    {Array.from({ length: rows }).map((_, rowIndex) => (
      <div
        key={rowIndex}
        className="grid gap-3"
        style={{ gridTemplateColumns: `repeat(${cols}, 1fr)` }}
      >
        {Array.from({ length: cols }).map((_, colIndex) => (
          <Skeleton key={colIndex} className="h-4 bg-slate-700" />
        ))}
      </div>
    ))}
  </div>
);
