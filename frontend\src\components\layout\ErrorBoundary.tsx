import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('<PERSON>rrorBounda<PERSON> caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });
  }

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  private handleReload = () => {
    window.location.reload();
  };

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen bg-slate-900 flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-slate-800 rounded-lg shadow-xl border border-slate-700 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <AlertTriangle className="text-red-400 w-6 h-6" />
              <h1 className="text-xl font-semibold text-slate-200">
                Something went wrong
              </h1>
            </div>
            
            <p className="text-slate-300 mb-6">
              An unexpected error occurred while loading this page. This has been logged for investigation.
            </p>

            <div className="flex space-x-3">
              <Button
                onClick={this.handleRetry}
                className="flex items-center space-x-2"
                variant="default"
              >
                <RefreshCw className="w-4 h-4" />
                <span>Try Again</span>
              </Button>
              
              <Button
                onClick={this.handleReload}
                variant="outline"
              >
                Reload Page
              </Button>
            </div>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-6">
                <summary className="text-sm text-slate-400 cursor-pointer hover:text-slate-300">
                  Error Details (Development)
                </summary>
                <div className="mt-2 p-3 bg-slate-900 rounded border border-slate-600">
                  <pre className="text-xs text-red-300 overflow-auto">
                    {this.state.error.toString()}
                    {this.state.errorInfo?.componentStack}
                  </pre>
                </div>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
} 