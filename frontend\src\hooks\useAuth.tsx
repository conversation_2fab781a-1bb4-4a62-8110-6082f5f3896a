import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from "react";
import { Session, User } from "@supabase/supabase-js";
import { API_BASE_URL } from "@/lib/config";

interface AuthContextType {
  session: Session | null;
  user: User | null;
  loading: boolean;
  signOut: () => Promise<void>;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signUp: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
}

// Export AuthContext for direct checking if needed elsewhere
export const AuthContext = createContext<AuthContextType | undefined>(
  undefined
);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Check for existing session on mount
  useEffect(() => {
    const checkSession = async () => {
      const token = localStorage.getItem('auth_token');
      const refreshToken = localStorage.getItem('refresh_token');

      if (token && token.trim() !== '') {
        try {
          const response = await fetch(`${API_BASE_URL}/auth/session`, {
            headers: {
              'Authorization': `Bearer ${token}`,
            },
          });

          if (response.ok) {
            const data = await response.json();
            setUser(data.user);
            // Create a minimal session object
            setSession({
              access_token: token,
              refresh_token: refreshToken || '',
              expires_in: 3600,
              token_type: 'bearer',
              user: data.user,
            } as Session);
          } else if (response.status === 401) {
            // Token is invalid/expired, try to refresh if we have a refresh token
            if (refreshToken) {
              try {
                const refreshResponse = await fetch(`${API_BASE_URL}/auth/refresh`, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({ refresh_token: refreshToken }),
                });

                if (refreshResponse.ok) {
                  const refreshData = await refreshResponse.json();
                  setUser(refreshData.user);
                  setSession(refreshData.session);
                  localStorage.setItem('auth_token', refreshData.session.access_token);
                  localStorage.setItem('refresh_token', refreshData.session.refresh_token);
                } else {
                  // Refresh failed, clear tokens
                  localStorage.removeItem('auth_token');
                  localStorage.removeItem('refresh_token');
                }
              } catch (refreshError) {
                console.error('Token refresh failed:', refreshError);
                localStorage.removeItem('auth_token');
                localStorage.removeItem('refresh_token');
              }
            } else {
              // No refresh token, clear everything
              localStorage.removeItem('auth_token');
              localStorage.removeItem('refresh_token');
            }
          } else {
            // Other error, clear tokens
            localStorage.removeItem('auth_token');
            localStorage.removeItem('refresh_token');
          }
        } catch (error) {
          console.error('Session check failed:', error);
          localStorage.removeItem('auth_token');
          localStorage.removeItem('refresh_token');
        }
      }

      setLoading(false);
    };

    checkSession();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      console.log(`🔐 Attempting to sign in with email: ${email}`);
      console.log(`🌐 API_BASE_URL: ${API_BASE_URL}`);
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
      
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      console.log(`📡 Sign-in response status: ${response.status}`);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ Sign-in failed with status ${response.status}:`, errorText);
        let errorMessage = "Login failed";
        
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          // If the response isn't JSON, use the text as is
          errorMessage = errorText || errorMessage;
        }
        
        return { success: false, error: errorMessage };
      }

      const data = await response.json();
      console.log("✅ Sign-in successful, received data:", data);

      if (data.success) {
        setSession(data.session);
        setUser(data.user);
        localStorage.setItem('auth_token', data.session.access_token);
        localStorage.setItem('refresh_token', data.session.refresh_token);
        return { success: true };
      } else {
        return { success: false, error: data.error || 'Login failed' };
      }
    } catch (error: any) {
      console.error("❌ Sign-in error:", error);
      
      // Provide more specific error messages
      if (error.name === 'AbortError') {
        return { success: false, error: 'Request timed out. Please check if the backend server is running.' };
      } else if (error.message === 'Failed to fetch') {
        return { success: false, error: 'Cannot connect to server. Please ensure the backend is running on port 5000.' };
      }
      
      return { success: false, error: error.message || 'Network error' };
    }
  };

  const signUp = async (email: string, password: string) => {
    try {
      console.log(`📝 Attempting to sign up with email: ${email}`);
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
      
      const response = await fetch(`${API_BASE_URL}/auth/signup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      console.log(`📡 Sign-up response status: ${response.status}`);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ Sign-up failed with status ${response.status}:`, errorText);
        let errorMessage = "Signup failed";
        
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          // If the response isn't JSON, use the text as is
          errorMessage = errorText || errorMessage;
        }
        
        return { success: false, error: errorMessage };
      }

      const data = await response.json();
      console.log("✅ Sign-up successful, received data:", data);

      if (data.success) {
        if (data.session) {
          setSession(data.session);
          setUser(data.user);
          localStorage.setItem('auth_token', data.session.access_token);
          localStorage.setItem('refresh_token', data.session.refresh_token);
        }
        return { success: true };
      } else {
        return { success: false, error: data.error || 'Signup failed' };
      }
    } catch (error: any) {
      console.error("❌ Sign-up error:", error);
      
      // Provide more specific error messages
      if (error.name === 'AbortError') {
        return { success: false, error: 'Request timed out. Please check if the backend server is running.' };
      } else if (error.message === 'Failed to fetch') {
        return { success: false, error: 'Cannot connect to server. Please ensure the backend is running on port 5000.' };
      }
      
      return { success: false, error: error.message || 'Network error' };
    }
  };

  const signOut = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      if (token && token.trim() !== '') {
        await fetch(`${API_BASE_URL}/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setSession(null);
      setUser(null);
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
    }
  };

  const value = {
    session,
    user,
    loading,
    signOut,
    signIn,
    signUp,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};