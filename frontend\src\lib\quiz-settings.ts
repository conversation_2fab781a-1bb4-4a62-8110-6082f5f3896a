import { QuizSettings, DEFAULT_QUIZ_SETTINGS } from "@/types/quiz-settings";

const QUIZ_SETTINGS_KEY = "quizSettings";

/**
 * Get quiz settings from localStorage
 */
export function getQuizSettings(): QuizSettings {
  const storedSettings = localStorage.getItem(QUIZ_SETTINGS_KEY);
  if (!storedSettings) {
    return DEFAULT_QUIZ_SETTINGS;
  }

  try {
    const parsedSettings = JSON.parse(storedSettings) as QuizSettings;
    // Merge with defaults to ensure all properties exist
    return { ...DEFAULT_QUIZ_SETTINGS, ...parsedSettings };
  } catch (error) {
    console.error("Failed to parse quiz settings:", error);
    return DEFAULT_QUIZ_SETTINGS;
  }
}

/**
 * Set quiz settings in localStorage
 */
export function setQuizSettings(settings: QuizSettings): void {
  localStorage.setItem(QUIZ_SETTINGS_KEY, JSON.stringify(settings));
}

/**
 * Update specific quiz setting
 */
export function updateQuizSetting<K extends keyof QuizSettings>(
  key: K,
  value: QuizSettings[K]
): void {
  const currentSettings = getQuizSettings();
  const updatedSettings = { ...currentSettings, [key]: value };
  setQuizSettings(updatedSettings);
}
