import React, { useState, useEffect } from "react";
import { Tables } from "../../types/supabase";
import { useAuth } from "../../hooks/useAuth";
import {
  generateAiQuizAPI,
  addQuestionsToQuizAPI,
  getDocumentsAPI,
  deleteQuizAPI,
  GenerateAiQuizApiResponse,
  GenerateAiQuizApiPayload,
  QuizQuestionBatchInsertPayload
} from "../../lib/api";
import {
  AiQuizGenerationOptions,
  GenerationOptions,
} from "./AiQuizGenerationOptions";
import { getAIProviderSettings } from "@/lib/ai-provider";
import { AIProviderConfig } from "@shared/types/quiz";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

interface AiQuestionGeneratorProps {
  selectedQuizId: string;
  selectedQuizName: string;
  studyDocumentId?: string;
  onGenerationComplete: () => void;
}

export const AiQuestionGenerator: React.FC<AiQuestionGeneratorProps> = ({
  selectedQuizId,
  selectedQuizName,
  studyDocumentId,
  onGenerationComplete,
}) => {
  const { user } = useAuth();
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationError, setGenerationError] = useState<string | null>(null);
  const [aiGenerationOptions, setAiGenerationOptions] =
    useState<GenerationOptions>({
      numberOfQuestions: 5,
      questionTypes: ["multiple_choice"],
    });
  const [availableDocuments, setAvailableDocuments] = useState<
    Tables<"study_documents">[]
  >([]);
  const [
    selectedDocumentIdsForGeneration,
    setSelectedDocumentIdsForGeneration,
  ] = useState<string[]>([]);
  const [customPrompt, setCustomPrompt] = useState("");
  const [loadingDocuments, setLoadingDocuments] = useState(false);

  useEffect(() => {
    const fetchDocuments = async () => {
      setLoadingDocuments(true);
      try {
        const docs = await getDocumentsAPI();
        setAvailableDocuments(docs || []);
        if (
          studyDocumentId &&
          docs.some(
            (doc: Tables<"study_documents">) => doc.id === studyDocumentId
          )
        ) {
          setSelectedDocumentIdsForGeneration([studyDocumentId]);
        }
      } catch (err: any) {
        console.error("Failed to fetch documents for AI generation:", err);
      } finally {
        setLoadingDocuments(false);
      }
    };
    fetchDocuments();
  }, [studyDocumentId]);

  const handleGenerateQuestions = async () => {
    if (!user || !selectedQuizId) {
      setGenerationError("User not logged in or quiz not selected.");
      return;
    }

    if (selectedDocumentIdsForGeneration.length === 0) {
      setGenerationError(
        "Please select at least one document to generate questions from."
      );
      return;
    }

    setIsGenerating(true);
    setGenerationError(null);

    try {
      // Create a temporary quiz name for the generation process
      const tempQuizName = `${selectedQuizName}_temp_${Date.now()}`;

      const payload: GenerateAiQuizApiPayload = {
        documentIds: selectedDocumentIdsForGeneration,
        quizName: tempQuizName,
        customPrompt: customPrompt || undefined,
        generationOptions: aiGenerationOptions,
        // aiConfig removed - credentials are retrieved from secure backend storage
      };

      // Generate a new quiz first (we'll extract its questions)
      const generationResult: GenerateAiQuizApiResponse =
        await generateAiQuizAPI(payload);

      console.log("AI Quiz Generation API result:", generationResult);

      if (
        generationResult &&
        generationResult.success &&
        generationResult.quiz &&
        generationResult.quiz.questions &&
        generationResult.quiz.questions.length > 0
      ) {
        // Questions are directly available in generationResult.quiz.questions
        const questionsFromTempQuiz = generationResult.quiz.questions;

        const questionsToAdd: QuizQuestionBatchInsertPayload[] = questionsFromTempQuiz.map(
          (q: any) => ({
            // The /generate endpoint maps db fields to client-facing names like questionText
            // We need to map them back to db field names for batch insert
            question_text: q.questionText || q.question_text, 
            type: q.type,
            options: q.options,
            correct_answer: q.correctAnswer || q.correct_answer,
            explanation: q.explanation,
          })
        );

        // Add these questions to the original quiz
        const addedQuestions = await addQuestionsToQuizAPI(
          selectedQuizId,
          questionsToAdd
        );
        console.log(
          `Added ${questionsToAdd.length} questions to quiz ${selectedQuizId}. Response:`,
          addedQuestions
        );

        // Delete the temporary quiz since we've moved its questions
        if (generationResult.quizId) { // Ensure quizId is present
          try {
            await deleteQuizAPI(generationResult.quizId);
            console.log(`Deleted temporary quiz ${generationResult.quizId}`);
          } catch (deleteError: any) {
            console.warn(`Failed to delete temporary quiz ${generationResult.quizId}: ${deleteError.message}`);
          }
        } else {
          console.warn("Temporary quizId not found in generationResult, cannot delete temporary quiz.");
        }
        onGenerationComplete();
      } else if (
        generationResult &&
        !generationResult.success
      ) {
        throw new Error(
          "AI Question generation was not successful according to the server."
        );
      } else if (generationResult && generationResult.quiz && (!generationResult.quiz.questions || generationResult.quiz.questions.length === 0)) {
        // Attempt to delete the empty temporary quiz if it was created
        if (generationResult.quizId) {
          try {
            await deleteQuizAPI(generationResult.quizId);
            console.log(`Deleted empty temporary quiz ${generationResult.quizId}`);
          }
          catch (deleteError: any) {
            console.warn(`Failed to delete empty temporary quiz ${generationResult.quizId}: ${deleteError.message}`);
          }
        }
        throw new Error("AI generated a quiz structure, but it contained no questions.");
      } else {
        throw new Error("Generation request was unsuccessful");
      }
    } catch (err: any) {
      console.error("Error during AI question generation:", err);
      setGenerationError(err.message || "Failed to generate questions.");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDocumentSelectionChange = (
    documentId: string,
    isSelected: boolean
  ) => {
    setSelectedDocumentIdsForGeneration((prev) =>
      isSelected
        ? [...prev, documentId]
        : prev.filter((id) => id !== documentId)
    );
  };

  return (
    <div className="bg-slate-900 border border-slate-600 rounded-xl p-6 shadow-lg">
      <div className="flex items-center justify-between border-b border-slate-700 pb-4 mb-6">
        <h5 className="text-lg font-semibold text-purple-400 flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
            className="w-5 h-5 mr-2 text-purple-400"
          >
            <path
              fillRule="evenodd"
              d="M10 2a8 8 0 100 16 8 8 0 000-16zM8.5 6.5a.5.5 0 00-1 0V8h-1.5a.5.5 0 000 1H8v1.5a.5.5 0 001 0V9h1.5a.5.5 0 000-1H9V6.5zm2.5 6a.5.5 0 00-1 0v.097A2.004 2.004 0 0010 13a2 2 0 100-4 .5.5 0 000 1 1 1 0 110 2 .5.5 0 00.5-.5V12.5z"
              clipRule="evenodd"
            />
          </svg>
          AI Question Generation
        </h5>
      </div>

      {generationError && (
        <div className="bg-red-900/20 border border-red-500/30 text-red-400 text-sm p-3 rounded-lg mb-4">
          {generationError}
        </div>
      )}

      <div className="space-y-4">
        <div>
          <Label
            htmlFor="documentSelect"
            className="text-slate-300 font-medium"
          >
            Select Document(s) for AI Generation*
          </Label>
          {loadingDocuments ? (
            <p className="text-sm text-slate-400 mt-2">Loading documents...</p>
          ) : availableDocuments.length === 0 ? (
            <p className="text-sm text-slate-400 mt-2">
              No documents available. Please upload a study document first.
            </p>
          ) : (
            <div className="max-h-40 overflow-y-auto bg-slate-800 border border-slate-600 rounded-md p-2 space-y-2 mt-2">
              {availableDocuments.map((doc) => (
                <div key={doc.id} className="flex items-center">
                  <input
                    id={`doc-${doc.id}`}
                    type="checkbox"
                    checked={selectedDocumentIdsForGeneration.includes(doc.id)}
                    onChange={(e) =>
                      handleDocumentSelectionChange(doc.id, e.target.checked)
                    }
                    className="h-4 w-4 text-purple-600 border-slate-500 rounded focus:ring-purple-500"
                  />
                  <label
                    htmlFor={`doc-${doc.id}`}
                    className="ml-2 block text-sm text-slate-300 truncate cursor-pointer"
                    title={doc.file_name}
                  >
                    {doc.file_name}
                  </label>
                </div>
              ))}
            </div>
          )}
          {selectedDocumentIdsForGeneration.length === 0 &&
            !loadingDocuments &&
            availableDocuments.length > 0 && (
              <p className="text-xs text-yellow-400 mt-1">
                Please select at least one document.
              </p>
            )}
        </div>

        <div>
          <Label htmlFor="customPrompt" className="text-slate-300 font-medium">
            Custom Instructions for AI{" "}
            <span className="text-slate-500">(Optional)</span>
          </Label>
          <textarea
            id="customPrompt"
            value={customPrompt}
            onChange={(e) => setCustomPrompt(e.target.value)}
            rows={3}
            placeholder="e.g., Focus on definitions, compare and contrast X and Y, generate questions suitable for a final exam..."
            className="block w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-md text-slate-200 placeholder-slate-400 focus:border-purple-500 focus:ring-1 focus:ring-purple-500 outline-none resize-none mt-2"
          />
        </div>

        <AiQuizGenerationOptions
          generationOptions={aiGenerationOptions}
          setGenerationOptions={setAiGenerationOptions}
          isGenerating={isGenerating}
          onGenerate={handleGenerateQuestions}
          documentIds={selectedDocumentIdsForGeneration}
        />
      </div>
    </div>
  );
};
