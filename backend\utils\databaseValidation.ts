/**
 * Database Validation Utilities
 * Provides comprehensive validation for database operations and schema integrity
 */

import { SupabaseClient } from "@supabase/supabase-js";

export interface FlashcardValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  data?: any;
}

/**
 * Validates flashcard data before database insertion
 */
export function validateFlashcardData(flashcards: any[], userId: string, setId: string): FlashcardValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate required parameters
  if (!userId) {
    errors.push("User ID is required for flashcard creation");
  }

  if (!setId) {
    errors.push("Set ID is required for flashcard creation");
  }

  if (!flashcards || !Array.isArray(flashcards)) {
    errors.push("Flashcards must be provided as an array");
    return { isValid: false, errors, warnings };
  }

  if (flashcards.length === 0) {
    warnings.push("No flashcards provided for insertion");
  }

  // Validate each flashcard
  flashcards.forEach((card, index) => {
    const cardErrors: string[] = [];

    // Check for required fields (flexible field names)
    const frontText = card.front_text || card.question;
    const backText = card.back_text || card.answer;

    if (!frontText || frontText.trim() === "") {
      cardErrors.push(`Card ${index + 1}: Front text/question is required`);
    }

    if (!backText || backText.trim() === "") {
      cardErrors.push(`Card ${index + 1}: Back text/answer is required`);
    }

    // Check for excessively long content
    if (frontText && frontText.length > 1000) {
      warnings.push(`Card ${index + 1}: Front text is very long (${frontText.length} characters)`);
    }

    if (backText && backText.length > 2000) {
      warnings.push(`Card ${index + 1}: Back text is very long (${backText.length} characters)`);
    }

    errors.push(...cardErrors);
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Validates user authentication context
 */
export function validateUserContext(user: any, authHeader?: string): FlashcardValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!user) {
    errors.push("User context is missing");
    return { isValid: false, errors, warnings };
  }

  if (!user.id) {
    errors.push("User ID is missing from user context");
  }

  if (typeof user.id !== "string") {
    errors.push(`User ID must be a string, got ${typeof user.id}`);
  }

  if (user.id && user.id.length !== 36) {
    warnings.push("User ID does not appear to be a valid UUID format");
  }

  if (!authHeader) {
    warnings.push("Authorization header is missing - may affect user-scoped operations");
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    data: {
      userId: user.id,
      userType: typeof user.id,
      hasAuthHeader: !!authHeader
    }
  };
}

/**
 * Tests database connectivity and permissions
 */
export async function testDatabaseConnection(supabase: SupabaseClient, userId: string): Promise<FlashcardValidationResult> {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    // Test basic connectivity by fetching flashcard sets
    const { data, error } = await supabase
      .from("flashcard_sets")
      .select("id, name")
      .eq("user_id", userId)
      .limit(1);

    if (error) {
      errors.push(`Database connection test failed: ${error.message}`);
      return { isValid: false, errors, warnings };
    }

    // Test write permissions by attempting a dry-run insert (will be rolled back)
    const testData = {
      user_id: userId,
      name: "TEST_CONNECTION_" + Date.now(),
      description: "Test connection - will be deleted",
    };

    const { data: testInsert, error: insertError } = await supabase
      .from("flashcard_sets")
      .insert(testData)
      .select("id")
      .single();

    if (insertError) {
      errors.push(`Database write test failed: ${insertError.message}`);
    } else if (testInsert) {
      // Clean up test data
      await supabase
        .from("flashcard_sets")
        .delete()
        .eq("id", testInsert.id);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      data: {
        canRead: !error,
        canWrite: !insertError,
        existingSets: data?.length || 0
      }
    };

  } catch (error: any) {
    errors.push(`Database test failed with exception: ${error.message}`);
    return { isValid: false, errors, warnings };
  }
}

/**
 * Logs validation results in a structured format
 */
export function logValidationResult(context: string, result: FlashcardValidationResult): void {
  console.log(`🔍 ${context} Validation Result:`);
  console.log(`- Status: ${result.isValid ? "✅ VALID" : "❌ INVALID"}`);
  
  if (result.errors.length > 0) {
    console.log("- Errors:");
    result.errors.forEach(error => console.log(`  ❌ ${error}`));
  }

  if (result.warnings.length > 0) {
    console.log("- Warnings:");
    result.warnings.forEach(warning => console.log(`  ⚠️ ${warning}`));
  }

  if (result.data) {
    console.log("- Additional Data:", result.data);
  }
}

/**
 * Comprehensive validation for flashcard creation workflow
 */
export async function validateFlashcardCreationWorkflow(
  flashcards: any[],
  user: any,
  setId: string,
  supabase: SupabaseClient,
  authHeader?: string
): Promise<FlashcardValidationResult> {
  const allErrors: string[] = [];
  const allWarnings: string[] = [];

  // Step 1: Validate user context
  const userValidation = validateUserContext(user, authHeader);
  logValidationResult("User Context", userValidation);
  allErrors.push(...userValidation.errors);
  allWarnings.push(...userValidation.warnings);

  if (!userValidation.isValid) {
    return { isValid: false, errors: allErrors, warnings: allWarnings };
  }

  // Step 2: Validate flashcard data
  const dataValidation = validateFlashcardData(flashcards, user.id, setId);
  logValidationResult("Flashcard Data", dataValidation);
  allErrors.push(...dataValidation.errors);
  allWarnings.push(...dataValidation.warnings);

  // Step 3: Test database connection (optional, can be skipped for performance)
  if (process.env.NODE_ENV === "development") {
    const dbValidation = await testDatabaseConnection(supabase, user.id);
    logValidationResult("Database Connection", dbValidation);
    allErrors.push(...dbValidation.errors);
    allWarnings.push(...dbValidation.warnings);
  }

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
    warnings: allWarnings,
    data: {
      userContext: userValidation.data,
      flashcardCount: flashcards.length,
      validationSteps: ["userContext", "flashcardData", "databaseConnection"]
    }
  };
}
