import React, { useEffect, useRef } from "react";
import { Route, RouteProps } from "wouter";

// Extends <PERSON><PERSON><PERSON>'s RouteProps. Component can be any valid React node.
interface AnimatedRouteProps extends Omit<RouteProps, "component"> {
  component: React.ComponentType<any>;
  // Add any additional props you might want to pass to the wrapper or route
}

const AnimatedRoute: React.FC<AnimatedRouteProps> = ({
  component: Component,
  ...rest
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Cleanup function to ensure proper unmounting
    return () => {
      if (containerRef.current) {
        // Force cleanup of any remaining event listeners or timers
        const container = containerRef.current;

        // Remove any lingering event listeners
        const events = ['keydown', 'keyup', 'click', 'focus', 'blur'];
        events.forEach(eventType => {
          container.removeEventListener(eventType, () => {});
        });
      }
    };
  }, []);

  return (
    <Route {...rest}>
      {(params) => (
        <div ref={containerRef} className="animate-fade-in">
          {/* Pass route params to the rendered component */}
          <Component {...params} />
        </div>
      )}
    </Route>
  );
};

export default AnimatedRoute;
