# ChewyAI Environment Configuration
# This is the root-level environment file for development orchestration
# Individual frontend and backend services have their own .env files

# Global Configuration
NODE_ENV=development

# Frontend Configuration
FRONTEND_PORT=3000
FRONTEND_URL=http://localhost:3000

# Backend Configuration
BACKEND_PORT=5000
BACKEND_URL=http://localhost:5000

# Shared Supabase Configuration
VITE_SUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.BKJfxPCJFHI-i_m7VE-JqVoUDVNXx2tE0qSDI4JJT2g
SUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0

# AI Provider Default Configuration (Optional - User will configure their own)
DEFAULT_AI_PROVIDER=openrouter
DEFAULT_AI_BASE_URL=https://openrouter.ai/api/v1
DEFAULT_EXTRACTION_MODEL=google/gemini-2.5-flash
DEFAULT_GENERATION_MODEL=google/gemini-2.5-pro

# Security
JWT_SECRET=your-jwt-secret-change-this-in-production
SESSION_SECRET=your-session-secret-change-this-in-production

# API Configuration
VITE_API_BASE_URL=http://localhost:5000/api

# Database Configuration (Optional - for SQLite local storage)
DATABASE_URL=./data/chewyai.sqlite 