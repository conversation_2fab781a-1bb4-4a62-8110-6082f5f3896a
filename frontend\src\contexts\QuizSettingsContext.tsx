import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { QuizSettings } from '@/types/quiz-settings';
import { getQuizSettings, setQuizSettings } from '@/lib/quiz-settings';

interface QuizSettingsContextType {
  settings: QuizSettings;
  updateSetting: <K extends keyof QuizSettings>(key: K, value: QuizSettings[K]) => void;
  resetSettings: () => void;
}

const QuizSettingsContext = createContext<QuizSettingsContextType | undefined>(undefined);

interface QuizSettingsProviderProps {
  children: ReactNode;
}

export const QuizSettingsProvider: React.FC<QuizSettingsProviderProps> = ({ children }) => {
  const [settings, setSettingsState] = useState<QuizSettings>(getQuizSettings);

  const updateSetting = <K extends keyof QuizSettings>(key: K, value: QuizSettings[K]) => {
    const newSettings = { ...settings, [key]: value };
    setSettingsState(newSettings);
    setQuizSettings(newSettings);
  };

  const resetSettings = () => {
    const defaultSettings = getQuizSettings();
    setSettingsState(defaultSettings);
    setQuizSettings(defaultSettings);
  };

  useEffect(() => {
    // Sync settings on mount
    const currentSettings = getQuizSettings();
    setSettingsState(currentSettings);
  }, []);

  return (
    <QuizSettingsContext.Provider value={{ settings, updateSetting, resetSettings }}>
      {children}
    </QuizSettingsContext.Provider>
  );
};

export const useQuizSettings = (): QuizSettingsContextType => {
  const context = useContext(QuizSettingsContext);
  if (context === undefined) {
    throw new Error('useQuizSettings must be used within a QuizSettingsProvider');
  }
  return context;
};
