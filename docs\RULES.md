# ChewyAI Development Rules

## 🎯 Core Principles

### 1. Security First
- **Never expose API keys to the client-side**
- All user-provided AI credentials are handled ephemerally by the backend
- Use environment variables for all sensitive configuration
- Implement proper authentication and authorization

### 2. Production-Ready Architecture
- Separate development and production configurations
- Optimize builds for performance and caching
- Implement proper error handling and logging
- Use TypeScript for type safety

### 3. API Design Standards
- All backend API endpoints MUST use the `/api` prefix
- Implement proper HTTP status codes and error responses
- Use consistent request/response formats
- Validate all inputs with Zod schemas

## 🏗️ Code Organization

### Frontend Structure
```
client/src/
├── components/     # Reusable UI components
├── pages/         # Route components
├── lib/           # Utilities and API clients
├── hooks/         # Custom React hooks
├── contexts/      # React contexts
└── types/         # TypeScript type definitions
```

### Backend Structure
```
server/
├── routes/        # API route handlers
├── middleware/    # Express middleware
├── db/           # Database configuration
└── config.ts     # Environment configuration
```

## 🔐 Security Rules

### Environment Variables
- Use `VITE_` prefix for client-side environment variables
- Never include fallback values for sensitive data in production
- Validate required environment variables on startup

### API Key Management
- User AI provider credentials are handled in-memory only
- No logging of sensitive credentials
- Backend-owned credentials managed via environment variables

### Authentication
- Use Supabase Auth for user authentication
- Implement proper JWT token validation
- Include authorization checks on all protected routes

## 📦 Package Management

### Dependencies
- Use npm for package management
- Keep dependencies up to date
- Separate dev and production dependencies
- Use exact versions for critical packages

### Build Process
- Clean dist folder before each build
- Optimize client bundle for production
- Minify server code for production
- Generate source maps for development only

## 🚀 Deployment Standards

### Production Build
- Set `NODE_ENV=production`
- Use optimized Vite build configuration
- Implement proper caching headers
- Include security headers

### Replit Configuration
- Use autoscale deployment target
- Configure proper health checks
- Set environment variables in deployment config
- Use compiled JavaScript (not TypeScript) for production

## 🧪 Testing Guidelines

### Code Quality
- Run TypeScript checks before deployment
- Test build process before production deployment
- Validate environment variable configuration
- Check API endpoint functionality

### Performance
- Monitor bundle sizes
- Optimize chunk splitting
- Implement proper caching strategies
- Use CDN for static assets when possible

## 📝 Documentation Standards

### Code Documentation
- Document all public APIs
- Include JSDoc comments for complex functions
- Maintain up-to-date README files
- Document environment variable requirements

### Architecture Documentation
- Keep MEMORIES.md updated with important decisions
- Document security practices in SECURITY.md
- Maintain deployment procedures in DEPLOYMENT.md
- Update API documentation when endpoints change
