import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";
import {
  createClient,
  SupabaseClient,
} from "https://esm.sh/@supabase/supabase-js@2";

// Define types for AI-generated questions (matching expected structure from AI)
interface AIGeneratedQuestionOption {
  text: string;
  is_correct: boolean;
}

interface AIGeneratedQuestion {
  question_text: string;
  type: "multiple_choice" | "select_all_that_apply" | "true_false" | "short_answer";
  options?: AIGeneratedQuestionOption[]; // For multiple_choice and select_all_that_apply
  correct_answer: string; // For true/false: "true" or "false". For short_answer: the answer. For MC: correct option text.
  explanation?: string;
}

interface GenerationOptions {
  numberOfQuestions: number;
  questionTypes: ("multiple_choice" | "select_all_that_apply" | "true_false" | "short_answer")[];
}

interface RequestBody {
  documentId: string;
  quizName: string;
  quizDescription?: string;
  generationOptions: GenerationOptions;
}

// Helper function to get user ID from Authorization header
async function getUserIdFromAuthHeader(
  req: Request,
  supabaseUrl: string,
  supabaseAnonKey: string
): Promise<string | null> {
  const authHeader = req.headers.get("Authorization");
  if (!authHeader) {
    console.warn("Missing Authorization header");
    return null;
  }
  try {
    const userSupabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
      global: { headers: { Authorization: authHeader } },
    });
    const {
      data: { user },
      error,
    } = await userSupabaseClient.auth.getUser();
    if (error) {
      console.error("Error getting user from auth header:", error.message);
      return null;
    }
    return user?.id || null;
  } catch (e) {
    console.error("Exception getting user from auth header:", e.message);
    return null;
  }
}

serve(async (req: Request) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const {
      documentId,
      quizName,
      quizDescription,
      generationOptions,
    }: RequestBody = await req.json();

    if (!documentId || !quizName || !generationOptions) {
      return new Response(
        JSON.stringify({
          error:
            "Missing documentId, quizName, or generationOptions in request body",
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }
    if (
      !generationOptions.numberOfQuestions ||
      !generationOptions.questionTypes ||
      generationOptions.questionTypes.length === 0
    ) {
      return new Response(
        JSON.stringify({
          error:
            "Invalid generationOptions: numberOfQuestions and questionTypes must be provided.",
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    const supabaseUrl = Deno.env.get("SUPABASE_URL") ?? "";
    const supabaseAnonKey = Deno.env.get("SUPABASE_ANON_KEY") ?? "";
    // Use Service Role Key for admin operations like inserting into tables
    const supabaseServiceRoleKey =
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "";

    if (!supabaseUrl || !supabaseAnonKey || !supabaseServiceRoleKey) {
      return new Response(
        JSON.stringify({ error: "Supabase environment variables not set." }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 500,
        }
      );
    }

    const openRouterApiKey = Deno.env.get("OPENROUTER_API_KEY");
    const openRouterBaseUrl =
      Deno.env.get("OPENROUTER_BASE_URL") || "https://openrouter.ai/api/v1";

    if (!openRouterApiKey) {
      return new Response(
        JSON.stringify({ error: "OpenRouter API key not configured." }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 500,
        }
      );
    }

    const userId = await getUserIdFromAuthHeader(
      req,
      supabaseUrl,
      supabaseAnonKey
    );
    if (!userId) {
      return new Response(
        JSON.stringify({ error: "Unauthorized: Could not verify user." }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 401,
        }
      );
    }

    // Initialize Supabase client with Service Role Key for database operations
    const supabase: SupabaseClient = createClient(
      supabaseUrl,
      supabaseServiceRoleKey
    );

    console.log(
      `Generating quiz '${quizName}' for document ${documentId}, user ${userId}, options:`,
      generationOptions
    );

    // 1. Fetch the document record
    const { data: document, error: dbError } = await supabase
      .from("study_documents") // Make sure this table name is correct
      .select("file_path, content_type, status, user_id, extracted_text_path")
      .eq("id", documentId)
      .single();

    if (dbError) {
      console.error("Error fetching document record:", dbError);
      return new Response(
        JSON.stringify({ error: "Failed to fetch document record" }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 500,
        }
      );
    }
    if (!document) {
      return new Response(JSON.stringify({ error: "Document not found" }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 404,
      });
    }
    // Optional: Check if the document belongs to the user making the request
    if (document.user_id && document.user_id !== userId) {
      return new Response(
        JSON.stringify({
          error: "Forbidden: Document does not belong to this user.",
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 403,
        }
      );
    }

    if (document.status !== "extracted") {
      return new Response(
        JSON.stringify({
          error: `Document status is '${document.status}'. Text extraction must be complete ('extracted').`,
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    // 2. Get file content from storage using download (secure)
    const filePath = document.extracted_text_path || document.file_path;

    let documentTextContent = "";
    try {
      // Use download method for private storage bucket
      const { data: fileData, error: downloadError } = await supabase.storage
        .from("study_materials")
        .download(filePath);

      if (downloadError || !fileData) {
        throw new Error(
          `Failed to download file: ${
            downloadError?.message || "Unknown error"
          }`
        );
      }

      // Convert blob to text
      documentTextContent = await fileData.text();

      if (!documentTextContent.trim()) {
        return new Response(
          JSON.stringify({ error: "Document content is empty." }),
          {
            headers: { ...corsHeaders, "Content-Type": "application/json" },
            status: 400,
          }
        );
      }

      console.log(
        `Extracted content (first 100 chars): ${documentTextContent.substring(
          0,
          100
        )}...`
      );
    } catch (fetchError) {
      console.error(
        "Error downloading/processing document content:",
        fetchError
      );
      return new Response(
        JSON.stringify({
          error: `Failed to process document: ${fetchError.message}`,
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 500,
        }
      );
    }

    // Validate that question types are provided and not empty
    if (!generationOptions.questionTypes || generationOptions.questionTypes.length === 0) {
      return new Response(
        JSON.stringify({
          error: "At least one question type must be selected for AI generation",
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    // 3. Generate AI prompt
    const questionTypesString = generationOptions.questionTypes.join(", ");
    console.log(`Supabase Function: Requested question types: [${questionTypesString}]`);

    const prompt = `
Given the following document content:
---
${documentTextContent}
---

Generate ${generationOptions.numberOfQuestions} quiz questions based on this content.

IMPORTANT: You MUST generate questions ONLY of the following types: ${questionTypesString}
Do NOT generate questions of any other types. Every single question must be one of these exact types: ${questionTypesString}.

For each question, provide:
1. question_text: The text of the question.
2. type: The type of question (must be one of: 'multiple_choice', 'select_all_that_apply', 'true_false', 'short_answer').
3. options: (ONLY for 'multiple_choice' and 'select_all_that_apply') An array of 3 to 4 option objects, where each object has a 'text' (string) and 'is_correct' (boolean) field. For 'multiple_choice', exactly one option must be correct. For 'select_all_that_apply', one or more options can be correct.
4. correct_answer: (For 'true_false', this must be the string 'true' or 'false'. For 'short_answer', this is the concise correct answer text. For 'multiple_choice', this should be the text of the correct option. For 'select_all_that_apply', this should be an array of strings with the text of all correct options).
5. explanation: (Optional) A brief explanation for why the answer is correct.

Return the output as a valid JSON array of question objects. Ensure the JSON is well-formed.
Example of a 'multiple_choice' question object:
{
  "question_text": "What is the primary color of the sky on a clear day?",
  "type": "multiple_choice",
  "options": [
    {"text": "Green", "is_correct": false},
    {"text": "Blue", "is_correct": true},
    {"text": "Red", "is_correct": false}
  ],
  "correct_answer": "Blue",
  "explanation": "The sky appears blue due to Rayleigh scattering of sunlight in the atmosphere."
}
Example of a 'select_all_that_apply' question object:
{
  "question_text": "Which of the following are programming languages?",
  "type": "select_all_that_apply",
  "options": [
    {"text": "JavaScript", "is_correct": true},
    {"text": "Python", "is_correct": true},
    {"text": "HTML", "is_correct": false},
    {"text": "Java", "is_correct": true}
  ],
  "correct_answer": ["JavaScript", "Python", "Java"],
  "explanation": "JavaScript, Python, and Java are programming languages, while HTML is a markup language."
}
Example of a 'true_false' question object:
{
  "question_text": "The sun revolves around the Earth.",
  "type": "true_false",
  "correct_answer": "false",
  "explanation": "The Earth revolves around the sun (heliocentric model)."
}
Example of a 'short_answer' question object:
{
  "question_text": "What is the chemical symbol for water?",
  "type": "short_answer",
  "correct_answer": "H2O",
  "explanation": "Water is composed of two hydrogen atoms and one oxygen atom."
}
Do NOT include any text outside the JSON array. The entire response should be a single JSON array.
`;

    // 4. Call OpenRouter AI
    let aiGeneratedQuestions: AIGeneratedQuestion[];
    try {
      console.log("Sending request to OpenRouter AI...");
      const aiResponse = await fetch(`${openRouterBaseUrl}/chat/completions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${openRouterApiKey}`,
          "HTTP-Referer": Deno.env.get("SITE_URL") || "http://localhost:3000",
          "X-Title": Deno.env.get("APP_NAME") || "AIPrdReviewer",
        },
        body: JSON.stringify({
          model: "google/gemini-2.5-pro-preview",
          messages: [{ role: "user", content: prompt }],
          temperature: 0.7,
          // max_tokens: 2048, // Consider if needed for longer documents/more questions
          // The model should ideally respect the JSON structure requested in the prompt.
          // If the model supports a specific JSON mode, that would be even better.
          // For Gemini, it often infers JSON output well if prompted correctly.
        }),
      });

      if (!aiResponse.ok) {
        const errorBody = await aiResponse.text();
        console.error(
          `OpenRouter API error: ${aiResponse.status} ${aiResponse.statusText}`,
          errorBody
        );
        throw new Error(
          `AI API request failed: ${aiResponse.statusText} - ${errorBody}`
        );
      }

      const result = await aiResponse.json();

      let questionsContent = result.choices?.[0]?.message?.content;
      if (typeof questionsContent === "string") {
        try {
          // Attempt to clean the string if it's wrapped in markdown code blocks
          const jsonMatch = questionsContent.match(
            /```json\s*([\s\S]*?)\s*```/
          );
          if (jsonMatch && jsonMatch[1]) {
            questionsContent = jsonMatch[1];
          }
          aiGeneratedQuestions = JSON.parse(questionsContent);
        } catch (parseError) {
          console.error(
            "Failed to parse AI response content string:",
            parseError,
            "Raw content:",
            questionsContent
          );
          throw new Error(
            "AI response content was a string but not valid JSON, or not in the expected array format."
          );
        }
      } else if (Array.isArray(questionsContent)) {
        // If content is already an array
        aiGeneratedQuestions = questionsContent;
      } else if (Array.isArray(result)) {
        // Fallback if the root of the response is the array
        aiGeneratedQuestions = result;
      } else {
        console.error(
          "Unexpected AI response structure, content is not a string or array:",
          result
        );
        throw new Error(
          "AI response was not in the expected format (JSON array of questions)."
        );
      }

      if (
        !Array.isArray(aiGeneratedQuestions) ||
        aiGeneratedQuestions.length === 0
      ) {
        console.warn(
          "AI generated no questions or returned an unexpected format.",
          aiGeneratedQuestions
        );
        throw new Error(
          "AI failed to generate questions or returned an empty/invalid set."
        );
      }
      // Filter questions to only include the requested types
      const originalCount = aiGeneratedQuestions.length;
      aiGeneratedQuestions = aiGeneratedQuestions.filter(q =>
        generationOptions.questionTypes.includes(q.type as any)
      );
      const filteredCount = aiGeneratedQuestions.length;

      if (originalCount !== filteredCount) {
        console.log(
          `Supabase Function: Filtered out ${originalCount - filteredCount} questions that didn't match requested types. ` +
          `Kept ${filteredCount} questions of types: [${generationOptions.questionTypes.join(", ")}]`
        );
      }

      console.log(
        `Successfully received ${aiGeneratedQuestions.length} questions from AI.`
      );
    } catch (aiError) {
      console.error("Error calling AI service:", aiError);
      return new Response(
        JSON.stringify({
          error: `Failed to generate questions with AI: ${aiError.message}`,
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 500,
        }
      );
    }

    // 5. Store quiz and questions in database
    const createdAt = Math.floor(Date.now() / 1000);

    const { data: newQuiz, error: quizInsertError } = await supabase
      .from("quizzes")
      .insert({
        name: quizName,
        description: quizDescription,
        document_id: parseInt(documentId, 10), // Ensure documentId is an integer
        user_id: userId,
        created_at: createdAt,
      })
      .select("id")
      .single();

    if (quizInsertError || !newQuiz) {
      console.error("Error inserting quiz:", quizInsertError);
      return new Response(JSON.stringify({ error: "Failed to save quiz" }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      });
    }
    const newQuizId = newQuiz.id;

    const questionsToInsert = aiGeneratedQuestions.map((q) => ({
      quiz_id: newQuizId,
      question_text: q.question_text,
      question_type: q.type,
      options: q.options ? JSON.stringify(q.options) : null,
      correct_answer: q.correct_answer,
      explanation: q.explanation,
      created_at: createdAt,
    }));

    const { error: questionsInsertError } = await supabase
      .from("quiz_questions")
      .insert(questionsToInsert);

    if (questionsInsertError) {
      console.error("Error inserting quiz questions:", questionsInsertError);
      await supabase.from("quizzes").delete().eq("id", newQuizId);
      return new Response(
        JSON.stringify({ error: "Failed to save quiz questions" }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 500,
        }
      );
    }

    console.log(
      `Quiz ${newQuizId} and ${questionsToInsert.length} questions created successfully.`
    );

    return new Response(JSON.stringify({ quizId: newQuizId }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error) {
    console.error("Unhandled error in generate-quiz-questions:", error);
    return new Response(
      JSON.stringify({
        error: error.message || "An unexpected error occurred",
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
