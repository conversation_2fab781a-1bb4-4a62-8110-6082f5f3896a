import React from "react";
import { useLocation } from "wouter";
import AppLayout from "@/components/layout/AppLayout";
import { CreateQuizForm } from "@/components/quiz/CreateQuizForm";
import { QuizList } from "@/components/quiz/QuizList";
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from "@/components/ui/card";

const QuizzesPage: React.FC = () => {
  const [, navigate] = useLocation();

  const handleQuizCreated = (quizId: string, quizName: string) => {
    // Navigate to the edit page for the newly created quiz
    navigate(`/quizzes/${quizId}/edit`);
  };

  const handleSelectQuiz = (quizId: string, quizName: string) => {
    navigate(`/quizzes/${quizId}/edit`);
  };

  const handlePlayQuiz = (quizId: string) => {
    navigate(`/quizzes/${quizId}`);
  };

  return (
    <AppLayout>
      <div className="max-w-4xl mx-auto p-4 md:p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-purple-400">Quizzes</h1>
        </div>
        {/* Quiz Form - Document selection is now handled within CreateQuizForm */}
        <CreateQuizForm onQuizCreated={handleQuizCreated} />
        {/* Quiz List */}
        <QuizList onSelectQuiz={handleSelectQuiz} onPlayQuiz={handlePlayQuiz} />
      </div>
    </AppLayout>
  );
};

export default QuizzesPage;
