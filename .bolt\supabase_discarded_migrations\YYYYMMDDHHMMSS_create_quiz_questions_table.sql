-- Migration to create the quiz_questions table

CREATE TABLE
  public.quiz_questions (
    id UUID NOT NULL DEFAULT gen_random_uuid (),
    quiz_id UUID NOT NULL REFERENCES public.quizzes (id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE, -- For RLS and direct queries if needed
    question_order INTEGER NOT NULL, -- To maintain question sequence
    question_text TEXT NOT NULL,
    options JSONB NOT NULL, -- e.g., ['Option A', 'Option B', 'Option C', 'Option D']
    correct_answer_index INTEGER NOT NULL, -- 0-based index into the options array
    explanation TEXT NULL, -- Optional explanation for the answer
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    CONSTRAINT quiz_questions_pkey PRIMARY KEY (id)
  );

-- Enable RLS
ALTER TABLE public.quiz_questions ENABLE ROW LEVEL SECURITY;

-- Policies for quiz_questions table
-- Users can select questions for quizzes they own
CREATE POLICY "Users can select questions for their quizzes" ON public.quiz_questions FOR
SELECT
  USING (
    auth.uid () = user_id
    AND EXISTS (
      SELECT
        1
      FROM
        public.quizzes q
      WHERE
        q.id = quiz_id
        AND q.user_id = auth.uid ()
    )
  );

-- Users can insert questions into their own quizzes
CREATE POLICY "Users can insert questions for their quizzes" ON public.quiz_questions FOR INSERT
WITH
  CHECK (
    auth.uid () = user_id
    AND EXISTS (
      SELECT
        1
      FROM
        public.quizzes q
      WHERE
        q.id = quiz_id
        AND q.user_id = auth.uid ()
    )
  );

-- Users can update questions in their own quizzes
CREATE POLICY "Users can update questions for their quizzes" ON public.quiz_questions FOR UPDATE
USING (
  auth.uid () = user_id
  AND EXISTS (
    SELECT
      1
    FROM
      public.quizzes q
    WHERE
      q.id = quiz_id
      AND q.user_id = auth.uid ()
  )
)
WITH
  CHECK (
    auth.uid () = user_id
    AND EXISTS (
      SELECT
        1
      FROM
        public.quizzes q
      WHERE
        q.id = quiz_id
        AND q.user_id = auth.uid ()
    )
  );

-- Users can delete questions from their own quizzes
CREATE POLICY "Users can delete questions from their quizzes" ON public.quiz_questions FOR DELETE USING (
  auth.uid () = user_id
  AND EXISTS (
    SELECT
      1
    FROM
      public.quizzes q
    WHERE
      q.id = quiz_id
      AND q.user_id = auth.uid ()
  )
);

-- Add indexes
CREATE INDEX idx_quiz_questions_quiz_id ON public.quiz_questions (quiz_id);
CREATE INDEX idx_quiz_questions_user_id ON public.quiz_questions (user_id);
CREATE INDEX idx_quiz_questions_order ON public.quiz_questions (quiz_id, question_order);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_quiz_question_updated_at ()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update updated_at on row update
CREATE TRIGGER on_quiz_question_updated BEFORE UPDATE ON public.quiz_questions
FOR EACH ROW
EXECUTE PROCEDURE public.handle_quiz_question_updated_at (); 