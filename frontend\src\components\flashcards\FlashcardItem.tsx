import React, { useState } from "react";
import { Flashcard as FlashcardType } from "@shared/types/flashcards"; // Using the shared type
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipP<PERSON>ider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { RotateCw } from "lucide-react"; // For a flip icon

interface FlashcardItemProps {
  flashcard: FlashcardType;
  showAnswerOverride?: boolean; // For quiz mode or always show answer
}

const FlashcardItem: React.FC<FlashcardItemProps> = ({
  flashcard,
  showAnswerOverride,
}) => {
  const [isFlipped, setIsFlipped] = useState(false);

  const handleFlip = () => {
    if (showAnswerOverride === undefined) {
      // Only allow manual flip if not overridden
      setIsFlipped(!isFlipped);
    }
  };

  const shouldShowAnswer =
    showAnswerOverride !== undefined ? showAnswerOverride : isFlipped;

  return (
    <Card
      className={`w-full max-w-md h-80 perspective-1000 cursor-pointer 
                    transition-transform duration-700 transform-style-preserve-3d 
                    ${shouldShowAnswer ? "rotate-y-180" : ""}
                    bg-purple-800 bg-opacity-20 backdrop-blur-md border-purple-700 text-slate-100
                  `}
      onClick={handleFlip}
    >
      {/* Front of the card (Question) */}
      <div
        className={`absolute w-full h-full backface-hidden 
                      ${shouldShowAnswer ? "opacity-0" : "opacity-100"} 
                      transition-opacity duration-300 delay-300`}
      >
        <CardHeader className="h-1/4">
          <CardTitle className="text-lg font-semibold text-purple-400">
            Question
          </CardTitle>
        </CardHeader>
        <CardContent className="h-3/4 flex items-center justify-center p-6">
          <p className="text-xl text-center text-slate-50">
            {flashcard.front_text}
          </p>
        </CardContent>
      </div>

      {/* Back of the card (Answer) */}
      <div
        className={`absolute w-full h-full backface-hidden rotate-y-180 
                      ${shouldShowAnswer ? "opacity-100" : "opacity-0"}
                      transition-opacity duration-300 delay-300`}
      >
        <CardHeader className="h-1/4">
          <CardTitle className="text-lg font-semibold text-green-400">
            Answer
          </CardTitle>
        </CardHeader>
        <CardContent className="h-3/4 flex items-center justify-center p-6">
          <p className="text-xl text-center text-slate-50">
            {flashcard.back_text}
          </p>
        </CardContent>
      </div>

      {showAnswerOverride === undefined && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={(e) => {
                  e.stopPropagation();
                  handleFlip();
                }}
                className="absolute bottom-4 right-4 text-purple-300 hover:text-purple-100 z-10"
                aria-label={shouldShowAnswer ? "Show question" : "Show answer"}
              >
                <RotateCw size={20} />
              </Button>
            </TooltipTrigger>
            <TooltipContent
              side="left"
              className="bg-slate-800 border-slate-600 text-slate-200"
            >
              <p>{shouldShowAnswer ? "Show question" : "Show answer"}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </Card>
  );
};

export default FlashcardItem;
