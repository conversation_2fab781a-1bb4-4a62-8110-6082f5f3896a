-- Create table for securely storing user AI provider credentials
CREATE TABLE IF NOT EXISTS user_ai_credentials (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    provider VARCHAR(50) NOT NULL,
    encrypted_api_key TEXT NOT NULL,
    encryption_iv VARCHAR(32) NOT NULL,
    encryption_tag VARCHAR(32) NOT NULL,
    base_url TEXT NOT NULL,
    extraction_model VARCHAR(100),
    generation_model VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one credential set per user per provider
    UNIQUE(user_id, provider)
);

-- Enable Row Level Security
ALTER TABLE user_ai_credentials ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only access their own credentials
CREATE POLICY "Users can view own AI credentials" ON user_ai_credentials
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own AI credentials" ON user_ai_credentials
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own AI credentials" ON user_ai_credentials
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own AI credentials" ON user_ai_credentials
    FOR DELETE USING (auth.uid() = user_id);

-- Create indexes for performance
CREATE INDEX idx_user_ai_credentials_user_id ON user_ai_credentials(user_id);
CREATE INDEX idx_user_ai_credentials_provider ON user_ai_credentials(provider);
CREATE INDEX idx_user_ai_credentials_user_provider ON user_ai_credentials(user_id, provider);

-- Add comments for documentation
COMMENT ON TABLE user_ai_credentials IS 'Securely stores encrypted user AI provider credentials';
COMMENT ON COLUMN user_ai_credentials.encrypted_api_key IS 'AES-256-GCM encrypted API key';
COMMENT ON COLUMN user_ai_credentials.encryption_iv IS 'Initialization vector for encryption';
COMMENT ON COLUMN user_ai_credentials.encryption_tag IS 'Authentication tag for encryption verification';
COMMENT ON COLUMN user_ai_credentials.provider IS 'AI provider name (e.g., OpenRouter, OpenAI)';
COMMENT ON COLUMN user_ai_credentials.base_url IS 'API base URL for the provider';
COMMENT ON COLUMN user_ai_credentials.extraction_model IS 'Model used for text extraction tasks';
COMMENT ON COLUMN user_ai_credentials.generation_model IS 'Model used for content generation tasks';
