import React from "react";
import Header from "./Header";
import Sidebar from "./Sidebar";
import MobileNav from "./MobileNav";
import { useAuth } from "../../hooks/useAuth";
import { SignInForm } from "../auth/SignInForm";
import { SignUpForm } from "../auth/SignUpForm";
import Spinner from "../ui/Spinner";

interface AppLayoutProps {
  children: React.ReactNode;
  title?: string;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children, title }) => {
  const { session, loading } = useAuth();
  const [sidebarOpen, setSidebarOpen] = React.useState(false);
  const [showSignUp, setShowSignUp] = React.useState(false);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen bg-slate-900">
        <Spinner size="lg" />
      </div>
    );
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-slate-900 flex flex-col justify-center items-center p-4">
        <div className="w-full max-w-md bg-slate-800 p-8 rounded-lg shadow-xl border border-slate-700">
          {showSignUp ? <SignUpForm /> : <SignInForm />}
          <button
            onClick={() => setShowSignUp(!showSignUp)}
            className="mt-6 text-sm text-purple-400 hover:text-purple-300 w-full"
          >
            {showSignUp
              ? "Already have an account? Sign In"
              : "Don't have an account? Sign Up"}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-slate-900">
      <Header toggleSidebar={toggleSidebar} title={title} />

      {/* Sidebar is now fixed position for desktop, and Sheet for mobile */}
      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      {/* Adjust main content to account for the fixed desktop sidebar */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <main className="flex-1 overflow-y-auto bg-slate-800 p-4 md:p-6 lg:p-8 md:ml-72 pt-20 md:pt-6">
          {/* pt-20 for mobile to account for sticky header, md:pt-6 as header is accounted for by sidebar's top padding*/}
          {children}
        </main>
      </div>

      {/* MobileNav can be part of the main content flow or overlayed, 
          depending on its design. For now, keeping it separate. 
          It should handle its own visibility based on screen size. */}
      <MobileNav />
    </div>
  );
};

export default AppLayout;
