#!/bin/bash
set -e

echo "🔧 Setting up ChewyAI development environment..."

# Update system packages
sudo apt-get update -qq

# Install Node.js 20 if not already installed
if ! command -v node &> /dev/null || [[ $(node -v | cut -d'v' -f2 | cut -d'.' -f1) -lt 20 ]]; then
    echo "📦 Installing Node.js 20..."
    curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Verify Node.js and npm versions
echo "📋 Node.js version: $(node -v)"
echo "📋 npm version: $(npm -v)"

# Navigate to workspace
cd /mnt/persist/workspace

# Install dependencies
echo "📦 Installing npm dependencies..."
npm install

# Verify TypeScript is available
echo "📋 TypeScript version: $(npx tsc --version)"

# Verify Jest is available
echo "📋 Jest version: $(npx jest --version)"

# Add npm global bin to PATH if not already there
if ! echo $PATH | grep -q "$(npm config get prefix)/bin"; then
    echo 'export PATH="$(npm config get prefix)/bin:$PATH"' >> $HOME/.profile
    export PATH="$(npm config get prefix)/bin:$PATH"
fi

echo "✅ Development environment setup complete!"
echo "🧪 Ready to run tests..."