import { Tables } from "./supabase";

// Direct exports of Supabase table types
export type Flashcard = Tables<"flashcards">;
export type FlashcardSet = Tables<"flashcard_sets">;
export type StudyDocument = Tables<"study_documents">;

// Type for the request body when generating flashcards
export interface GenerateFlashcardsRequest {
  textContent: string;
  documentId: string;
  deckTitle?: string;
}

// Type for the API response when flashcards are generated
export interface GenerateFlashcardsResponse {
  set: FlashcardSet;
  flashcards: Flashcard[];
}
