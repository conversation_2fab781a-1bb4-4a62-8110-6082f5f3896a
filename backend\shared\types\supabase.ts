export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  public: {
    Tables: {
      flashcard_sets: {
        Row: {
          created_at: string | null;
          description: string | null;
          id: string;
          name: string;
          study_document_id: string | null;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          created_at?: string | null;
          description?: string | null;
          id?: string;
          name: string;
          study_document_id?: string | null;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          created_at?: string | null;
          description?: string | null;
          id?: string;
          name?: string;
          study_document_id?: string | null;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "flashcard_sets_study_document_id_fkey";
            columns: ["study_document_id"];
            isOneToOne: false;
            referencedRelation: "study_documents";
            referencedColumns: ["id"];
          }
        ];
      };
      flashcards: {
        Row: {
          back_text: string;
          created_at: string | null;
          front_text: string;
          id: string;
          set_id: string;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          back_text: string;
          created_at?: string | null;
          front_text: string;
          id?: string;
          set_id: string;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          back_text?: string;
          created_at?: string | null;
          front_text?: string;
          id?: string;
          set_id?: string;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "flashcards_set_id_fkey";
            columns: ["set_id"];
            isOneToOne: false;
            referencedRelation: "flashcard_sets";
            referencedColumns: ["id"];
          }
        ];
      };
      profiles: {
        Row: {
          avatar_url: string | null;
          email: string | null;
          full_name: string | null;
          id: string;
          updated_at: string | null;
          username: string | null;
        };
        Insert: {
          avatar_url?: string | null;
          email?: string | null;
          full_name?: string | null;
          id: string;
          updated_at?: string | null;
          username?: string | null;
        };
        Update: {
          avatar_url?: string | null;
          email?: string | null;
          full_name?: string | null;
          id?: string;
          updated_at?: string | null;
          username?: string | null;
        };
        Relationships: [];
      };
      quiz_questions: {
        Row: {
          correct_answer: string | null;
          created_at: string | null;
          due_at: string | null;
          explanation: string | null;
          id: string;
          last_reviewed_at: string | null;
          options: Json | null;
          question_text: string;
          quiz_id: string;
          srs_correct_streak: number | null;
          srs_ease_factor: number | null;
          srs_interval: number | null;
          srs_level: number | null;
          srs_repetitions: number | null;
          type: Database["public"]["Enums"]["question_type"];
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          correct_answer?: string | null;
          created_at?: string | null;
          due_at?: string | null;
          explanation?: string | null;
          id?: string;
          last_reviewed_at?: string | null;
          options?: Json | null;
          question_text: string;
          quiz_id: string;
          srs_correct_streak?: number | null;
          srs_ease_factor?: number | null;
          srs_interval?: number | null;
          srs_level?: number | null;
          srs_repetitions?: number | null;
          type: Database["public"]["Enums"]["question_type"];
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          correct_answer?: string | null;
          created_at?: string | null;
          due_at?: string | null;
          explanation?: string | null;
          id?: string;
          last_reviewed_at?: string | null;
          options?: Json | null;
          question_text?: string;
          quiz_id?: string;
          srs_correct_streak?: number | null;
          srs_ease_factor?: number | null;
          srs_interval?: number | null;
          srs_level?: number | null;
          srs_repetitions?: number | null;
          type?: Database["public"]["Enums"]["question_type"];
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "quiz_questions_quiz_id_fkey";
            columns: ["quiz_id"];
            isOneToOne: false;
            referencedRelation: "quizzes";
            referencedColumns: ["id"];
          }
        ];
      };
      quizzes: {
        Row: {
          created_at: string | null;
          description: string | null;
          id: string;
          name: string;
          study_document_id: string | null;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          created_at?: string | null;
          description?: string | null;
          id?: string;
          name: string;
          study_document_id?: string | null;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          created_at?: string | null;
          description?: string | null;
          id?: string;
          name?: string;
          study_document_id?: string | null;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "quizzes_study_document_id_fkey";
            columns: ["study_document_id"];
            isOneToOne: false;
            referencedRelation: "study_documents";
            referencedColumns: ["id"];
          }
        ];
      };
      study_documents: {
        Row: {
          content_type: string | null;
          created_at: string | null;
          extracted_text_path: string | null;
          extracted_text_summary: string | null;
          file_name: string;
          file_path: string;
          id: string;
          size_bytes: number | null;
          status: string | null;
          storage_object_id: string | null;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          content_type?: string | null;
          created_at?: string | null;
          extracted_text_path?: string | null;
          extracted_text_summary?: string | null;
          file_name: string;
          file_path: string;
          id?: string;
          size_bytes?: number | null;
          status?: string | null;
          storage_object_id?: string | null;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          content_type?: string | null;
          created_at?: string | null;
          extracted_text_path?: string | null;
          extracted_text_summary?: string | null;
          file_name?: string;
          file_path?: string;
          id?: string;
          size_bytes?: number | null;
          status?: string | null;
          storage_object_id?: string | null;
          updated_at?: string | null;
          user_id?: string;        };
        Relationships: [];
      };
      user_completions: {
        Row: {
          completed_at: string;
          completion_type: string;
          correct_answers: number | null;
          created_at: string;
          flashcard_set_id: string | null;
          id: string;
          metadata: Json | null;
          questions_answered: number | null;
          quiz_id: string | null;
          score: number | null;
          time_spent_minutes: number | null;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          completed_at?: string;
          completion_type: string;
          correct_answers?: number | null;
          created_at?: string;
          flashcard_set_id?: string | null;
          id?: string;
          metadata?: Json | null;
          questions_answered?: number | null;
          quiz_id?: string | null;
          score?: number | null;
          time_spent_minutes?: number | null;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          completed_at?: string;
          completion_type?: string;
          correct_answers?: number | null;
          created_at?: string;
          flashcard_set_id?: string | null;
          id?: string;
          metadata?: Json | null;
          questions_answered?: number | null;
          quiz_id?: string | null;
          score?: number | null;
          time_spent_minutes?: number | null;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "user_completions_flashcard_set_id_fkey";
            columns: ["flashcard_set_id"];
            isOneToOne: false;
            referencedRelation: "flashcard_sets";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "user_completions_quiz_id_fkey";
            columns: ["quiz_id"];
            isOneToOne: false;
            referencedRelation: "quizzes";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "user_completions_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      question_type:
        | "multiple_choice"
        | "true_false"
        | "short_answer"
        | "fill_in_the_blank"
        | "select_all_that_apply";
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
      DefaultSchema["Views"])
  ? (DefaultSchema["Tables"] &
      DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
      Row: infer R;
    }
    ? R
    : never
  : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Insert: infer I;
    }
    ? I
    : never
  : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Update: infer U;
    }
    ? U
    : never
  : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
  ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
  : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
  ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never;

export const Constants = {
  public: {
    Enums: {
      question_type: [
        "multiple_choice",
        "true_false",
        "short_answer",
        "fill_in_the_blank",
        "select_all_that_apply",
      ],
    },
  },
} as const;
