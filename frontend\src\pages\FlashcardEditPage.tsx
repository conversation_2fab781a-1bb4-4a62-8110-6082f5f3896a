import React, { useEffect, useState } from "react";
import { useParams, useLocation } from "wouter";
import AppLayout from "@/components/layout/AppLayout";
import FlashcardManager from "@/components/flashcards/FlashcardEditManager";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";
// Removed direct Supabase import - using backend API endpoints
import { useAuth } from "@/hooks/useAuth";
import Spinner from "@/components/ui/Spinner";

interface DeckData {
  id: string;
  name: string;
}

const FlashcardEditPage: React.FC = () => {
  const params = useParams<{ deckId: string }>();
  const [, navigate] = useLocation();
  const { user } = useAuth();
  const deckId = params?.deckId;

  const [deck, setDeck] = useState<DeckData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDeckDataFromBackend = async () => {
      if (!user || !deckId) {
        setIsLoading(false);
        if (!user) setError("User not authenticated.");
        if (!deckId) setError("Deck ID not provided.");
        return;
      }

      setError(null);
      setIsLoading(true);

      try {
        const token = localStorage.getItem('auth_token');

        if (!token) {
          setError("Authentication token not found. Please log in.");
          return;
        }

        const response = await fetch(`/api/flashcard-sets/${deckId}`, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          let errorMsg = `Failed to fetch flashcard set: ${response.statusText} (Status: ${response.status})`;
          if (response.status === 404) {
            errorMsg = "Flashcard set not found or you don't have access.";
          }
          try {
            const errorData = await response.json();
            errorMsg = errorData.error || errorData.message || errorMsg;
          } catch (e) {
            // Ignore if response is not JSON
          }
          setError(errorMsg);
          return;
        }

        const data: DeckData = await response.json();
        setDeck(data);
      } catch (err: any) {
        console.error("Error fetching flashcard set from backend:", err);
        if (!error) {
          setError(
            err.message ||
              "An unexpected error occurred while fetching the flashcard set."
          );
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchDeckDataFromBackend();
  }, [user, deckId]);

  const handleClose = () => {
    navigate("/flashcards");
  };

  if (isLoading) {
    return (
      <AppLayout title="Loading...">
        <div className="container mx-auto px-4 py-8 flex justify-center items-center h-64">
          <Spinner size="lg" />
          <span className="ml-3 text-slate-300">Loading flashcard set...</span>
        </div>
      </AppLayout>
    );
  }

  if (error || !deck) {
    return (
      <AppLayout title="Error">
        <div className="container mx-auto px-4 py-8">
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6 text-center">
              <AlertTriangle className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2 text-purple-400">
                {error ? "Error Loading Deck" : "Flashcard Set Not Found"}
              </h3>
              <p className="text-slate-300 mb-4">
                {error ||
                  "The flashcard set you're looking for doesn't exist or you don't have permission to access it."}
              </p>
              <Button
                onClick={handleClose}
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                Return to Flashcards
              </Button>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout title={`Edit: ${deck.name}`}>
      <div className="container mx-auto px-4 py-8">
        <FlashcardManager
          selectedDeckId={deck.id}
          selectedDeckName={deck.name}
          onClose={handleClose}
        />
      </div>
    </AppLayout>
  );
};

export default FlashcardEditPage;
