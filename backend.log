
> chewy-ai@0.0.0 server
> tsx server/index.ts


node:internal/modules/run_main:122
    triggerUncaughtException(
    ^
Error [ERR_MODULE_NOT_FOUND]: Cannot find module 'C:\Users\<USER>\Documents\Github\chewyai\server\index.ts' imported from C:\Users\<USER>\Documents\Github\chewyai\
    at finalizeResolution (node:internal/modules/esm/resolve:275:11)
    at moduleResolve (node:internal/modules/esm/resolve:860:10)
    at defaultResolve (node:internal/modules/esm/resolve:984:11)
    at nextResolve (node:internal/modules/esm/hooks:748:28)
    at resolveBase (file:///C:/Users/<USER>/Documents/Github/chewyai/node_modules/tsx/dist/esm/index.mjs?1750375987291:2:3212)
    at resolveDirectory (file:///C:/Users/<USER>/Documents/Github/chewyai/node_modules/tsx/dist/esm/index.mjs?1750375987291:2:3584)
    at resolveTsPaths (file:///C:/Users/<USER>/Documents/Github/chewyai/node_modules/tsx/dist/esm/index.mjs?1750375987291:2:4073)
    at resolve (file:///C:/Users/<USER>/Documents/Github/chewyai/node_modules/tsx/dist/esm/index.mjs?1750375987291:2:4447)
    at nextResolve (node:internal/modules/esm/hooks:748:28)
    at Hooks.resolve (node:internal/modules/esm/hooks:240:30) {
  code: 'ERR_MODULE_NOT_FOUND',
  url: 'file:///C:/Users/<USER>/Documents/Github/chewyai/server/index.ts'
}

Node.js v22.14.0
