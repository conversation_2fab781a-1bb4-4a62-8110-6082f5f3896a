modules = ["nodejs-20", "web"]
run = "bash start-dev-clean.sh"

hidden = [".config", ".git", "generated-icon.png", "node_modules", "dist"]

[nix]
channel = "stable-24_05"
packages = ["supabase-cli"]

[env]
PORT = "5000"
FRONTEND_PORT = "3000"
VITE_API_BASE_URL = "https://chewy-ai.replit.app:5000/api"

[deployment]
deploymentTarget = "autoscale"
build = ["sh", "-c", "npm run build"]
run = ["sh", "-c", "bash start-production-replit.sh"]
healthcheck = "/health"

[[ports]]
localPort = 80
externalPort = 3000

[[ports]]
localPort = 3000
externalPort = 80

[[ports]]
<<<<<<< HEAD
localPort = 3000
externalPort = 80
=======
localPort = 5000
externalPort = 5000

[[ports]]
localPort = 24678
>>>>>>> 366134829ca7366f1440a0df99db6a8eb2de9639

[[ports]]
localPort = 5000
externalPort = 5000

[workflows]
runButton = "Project"

[[workflows.workflow]]
name = "Project"
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
<<<<<<< HEAD
args = "npm run dev"
waitForPort = 3000
=======
args = "bash start-dev-clean.sh"
waitForPort = 5000
>>>>>>> 366134829ca7366f1440a0df99db6a8eb2de9639
