#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}Starting clean development environment...${NC}"

# Function to check if a port is in use
check_port() {
    port=$1
    if netstat -tuln 2>/dev/null | grep -E "(^|[^0-9]):$port " | grep "LISTEN" >/dev/null; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to kill processes on a port
kill_port() {
    port=$1
    echo -e "${YELLOW}Checking for processes on port $port...${NC}"
    
    if check_port $port; then
        echo -e "${YELLOW}Port $port is in use. Attempting to free it...${NC}"
        
        # Get PIDs using the port (fallback to pkill if lsof not available)
        pids=$(netstat -tulnp 2>/dev/null | grep -E "(^|[^0-9]):$port " | grep "LISTEN" | awk '{print $7}' | cut -d'/' -f1 | grep -v '^-$' | sort -u)
        
        if [ ! -z "$pids" ]; then
            echo -e "${YELLOW}Found processes: $pids${NC}"
            
            # Try graceful shutdown first
            echo -e "${YELLOW}Sending SIGTERM to processes...${NC}"
            echo $pids | xargs kill -TERM 2>/dev/null || true
            
            # Wait a moment for graceful shutdown
            sleep 2
            
            # Check if processes are still running
            if check_port $port; then
                echo -e "${RED}Forcing shutdown with SIGKILL...${NC}"
                echo $pids | xargs kill -KILL 2>/dev/null || true
                sleep 1
            fi
        else
            # Fallback: kill by process name pattern
            echo -e "${YELLOW}Using fallback process cleanup...${NC}"
            pkill -f ":$port" 2>/dev/null || true
            sleep 1
        fi
        
        # Final check
        if check_port $port; then
            echo -e "${RED}Failed to free port $port${NC}"
            return 1
        else
            echo -e "${GREEN}Port $port is now free${NC}"
            return 0
        fi
    else
        echo -e "${GREEN}Port $port is already free${NC}"
        return 0
    fi
}

# Clean up any existing processes
echo -e "${BLUE}Cleaning up existing processes...${NC}"

# Kill processes on development ports
kill_port 3000  # Frontend
kill_port 5000  # Backend
kill_port 24678 # HMR WebSocket (Replit)
kill_port 24679 # HMR WebSocket (Local)

# Kill any node processes that might be hanging
echo -e "${YELLOW}Cleaning up any hanging Node.js processes...${NC}"
pkill -f "node.*server" 2>/dev/null || true
pkill -f "vite" 2>/dev/null || true
sleep 1

# Verify environment files exist
echo -e "${BLUE}Checking environment configuration...${NC}"

if [ ! -f ".env" ]; then
    echo -e "${YELLOW}.env file not found. Creating from .env.example...${NC}"
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo -e "${GREEN}Created .env file${NC}"
    else
        echo -e "${RED}.env.example not found. Please create .env manually${NC}"
    fi
fi

if [ ! -f "client/.env" ]; then
    echo -e "${YELLOW}client/.env file not found. Creating from client/.env.example...${NC}"
    if [ -f "client/.env.example" ]; then
        cp client/.env.example client/.env
        echo -e "${GREEN}Created client/.env file${NC}"
    else
        echo -e "${YELLOW}client/.env.example not found. Skipping...${NC}"
    fi
fi

# Install dependencies if needed
echo -e "${BLUE}Checking dependencies...${NC}"

if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}Installing root dependencies...${NC}"
    npm install
fi

if [ ! -d "client/node_modules" ]; then
    echo -e "${YELLOW}Installing client dependencies...${NC}"
    cd client && npm install && cd ..
fi

# Build TypeScript files if needed
echo -e "${BLUE}Building TypeScript files...${NC}"
npm run build:server 2>/dev/null || echo -e "${YELLOW}No build:server script found, skipping...${NC}"

# Start the backend server first
echo -e "${BLUE}Starting backend server...${NC}"

# Set environment variables for development
export NODE_ENV=development
export PORT=5000

# Start backend in background with proper logging
echo -e "${GREEN}Starting backend server on port 5000...${NC}"
npm run server > backend.log 2>&1 &
BACKEND_PID=$!

# Wait for backend to start
echo -e "${YELLOW}Waiting for backend server to start...${NC}"
sleep 3

# Check if backend is running
backend_attempts=0
max_backend_attempts=10

while [ $backend_attempts -lt $max_backend_attempts ]; do
    if check_port 5000; then
        echo -e "${GREEN}Backend server is running on port 5000${NC}"
        break
    else
        echo -e "${YELLOW}Backend not ready yet (attempt $((backend_attempts + 1))/$max_backend_attempts)...${NC}"
        sleep 2
        backend_attempts=$((backend_attempts + 1))
    fi
done

if [ $backend_attempts -eq $max_backend_attempts ]; then
    echo -e "${RED}Backend server failed to start after $max_backend_attempts attempts${NC}"
    echo -e "${RED}Backend logs:${NC}"
    tail -20 backend.log
    exit 1
fi

# Test backend health endpoint
echo -e "${BLUE}Testing backend health endpoint...${NC}"
health_response=$(curl -s -w "%{http_code}" -o /dev/null http://localhost:5000/api/health 2>/dev/null || echo "000")

if [ "$health_response" = "200" ]; then
    echo -e "${GREEN}Backend health check passed${NC}"
else
    echo -e "${YELLOW}Backend health check returned status: $health_response${NC}"
    echo -e "${YELLOW}Recent backend logs:${NC}"
    tail -10 backend.log
fi

# Start the frontend server
echo -e "${BLUE}Starting frontend server...${NC}"

# Start frontend in background
cd client
npm run dev > ../frontend.log 2>&1 &
FRONTEND_PID=$!
cd ..

# Wait for frontend to start
echo -e "${YELLOW}Waiting for frontend server to start...${NC}"
sleep 3

# Check if frontend is running
frontend_attempts=0
max_frontend_attempts=10

while [ $frontend_attempts -lt $max_frontend_attempts ]; do
    if check_port 3000; then
        echo -e "${GREEN}Frontend server is running on port 3000${NC}"
        break
    else
        echo -e "${YELLOW}Frontend not ready yet (attempt $((frontend_attempts + 1))/$max_frontend_attempts)...${NC}"
        sleep 2
        frontend_attempts=$((frontend_attempts + 1))
    fi
done

if [ $frontend_attempts -eq $max_frontend_attempts ]; then
    echo -e "${RED}Frontend server failed to start after $max_frontend_attempts attempts${NC}"
    echo -e "${RED}Frontend logs:${NC}"
    tail -20 frontend.log
    exit 1
fi

# Final status check
echo -e "${BLUE}Final status check...${NC}"
echo -e "${GREEN}Development environment is ready!${NC}"
echo -e "${BLUE}Server Status:${NC}"
echo -e "   Frontend: http://localhost:3000"
echo -e "   Backend:  http://localhost:5000"
echo -e "   Health:   http://localhost:5000/api/health"
echo -e ""
echo -e "${BLUE}Process IDs:${NC}"
echo -e "   Backend PID: $BACKEND_PID"
echo -e "   Frontend PID: $FRONTEND_PID"
echo -e ""
echo -e "${YELLOW}To stop the servers, run: kill $BACKEND_PID $FRONTEND_PID${NC}"
echo -e "${YELLOW}Logs are available in: backend.log and frontend.log${NC}"
echo -e ""
echo -e "${GREEN}You can now access the application at http://localhost:3000${NC}"

# Keep the script running and show logs
echo -e "${BLUE}Showing live logs (Ctrl+C to stop):${NC}"
echo -e "${YELLOW}--- Backend Logs ---${NC}"
tail -f backend.log &
TAIL_BACKEND_PID=$!

echo -e "${YELLOW}--- Frontend Logs ---${NC}"
tail -f frontend.log &
TAIL_FRONTEND_PID=$!

# Handle cleanup on script exit
cleanup() {
    echo -e "\n${YELLOW}Shutting down development environment...${NC}"
    
    # Kill log tails
    kill $TAIL_BACKEND_PID 2>/dev/null || true
    kill $TAIL_FRONTEND_PID 2>/dev/null || true
    
    # Kill servers
    kill $BACKEND_PID 2>/dev/null || true
    kill $FRONTEND_PID 2>/dev/null || true
    
    # Wait a moment for graceful shutdown
    sleep 2
    
    # Force kill if needed
    kill -9 $BACKEND_PID 2>/dev/null || true
    kill -9 $FRONTEND_PID 2>/dev/null || true
    
    echo -e "${GREEN}Development environment stopped${NC}"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Wait for user to stop the script
wait