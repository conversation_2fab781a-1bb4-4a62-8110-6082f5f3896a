import express, { Request, Response } from "express";
import { db } from "../db/drizzle";
import { flashcardDecks, insertFlashcardDeckSchema } from "../../shared/schema";
import { eq } from "drizzle-orm";
import { supabaseClient } from "../middleware/supabaseMiddleware";
import { z } from "zod";

const router = express.Router();

// Helper function to verify the current user is authenticated
async function getAuthenticatedUser(req: Request): Promise<
  | { error: string; details?: string; status: number }
  | { user: { id: string; [key: string]: any } }
> {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return { error: "Unauthorized: Missing or malformed token", status: 401 };
    }
    const token = authHeader.split(" ")[1];
    if (!token) {
      return { error: "Unauthorized: Missing token", status: 401 };
    }
    const supabase = supabaseClient;
    const { data, error: getUserError } = await supabase.auth.getUser(token);
    if (getUserError) {
      if (
        getUserError.message.toLowerCase().includes("invalid token") ||
        getUserError.message.includes("jwt")
      ) {
        return {
          error: "Unauthorized: Invalid token",
          details: getUserError.message,
          status: 401,
        };
      }
      return {
        error: "Server error validating token",
        details: getUserError.message,
        status: 500,
      };
    }
    const user = data?.user;
    if (!user) {
      return { error: "Unauthorized: No user found for token", status: 401 };
    }
    if (!user.id) {
      return { error: "User ID missing from authenticated user", status: 500 };
    }
    return { user: { ...user } };
  } catch (err: any) {
    console.error("Auth error in flashcardDeckRoutes:", err.message, err.stack);
    return { error: "Authentication error", status: 500 };
  }
}

// POST /api/decks - Create a new flashcard deck
router.post("/", async (req: Request, res: Response) => {
  const authResult = await getAuthenticatedUser(req);
  if ("error" in authResult) {
    return res
      .status(authResult.status)
      .json({ error: authResult.error, details: authResult.details });
  }
  const user = authResult.user;
  try {
    const body = req.body;
    const parsedBody = insertFlashcardDeckSchema
      .omit({ userId: true })
      .safeParse(body);
    if (!parsedBody.success) {
      return res
        .status(400)
        .json({ error: "Invalid deck data", details: parsedBody.error.flatten() });
    }
    const deckData = {
      ...parsedBody.data,
      userId: user.id,
      createdAt: Date.now(),
    };
    const newDeck = await db.insert(flashcardDecks).values(deckData).returning();
    if (!newDeck || newDeck.length === 0) {
      return res.status(500).json({ error: "Failed to create deck" });
    }
    return res.status(201).json(newDeck[0]);
  } catch (error: any) {
    console.error("Error creating deck:", error);
    return res
      .status(500)
      .json({ error: "Failed to create deck", details: error.message });
  }
});

// GET /api/decks - Get all decks for the authenticated user
router.get("/", async (req: Request, res: Response) => {
  const authResult = await getAuthenticatedUser(req);
  if ("error" in authResult) {
    return res
      .status(authResult.status)
      .json({ error: authResult.error, details: authResult.details });
  }
  const user = authResult.user;
  try {
    const userDecks = await db
      .select()
      .from(flashcardDecks)
      .where(eq(flashcardDecks.userId, user.id));
    return res.status(200).json(userDecks);
  } catch (error: any) {
    console.error("Error fetching decks:", error);
    return res
      .status(500)
      .json({ error: "Failed to fetch decks", details: error.message });
  }
});

export default router;
