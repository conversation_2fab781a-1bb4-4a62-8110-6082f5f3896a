import { Tables, Database } from "./supabase";

export interface AIProviderConfig {
  apiKey: string;
  baseUrl: string;
  model: string;
}

export interface QuizQuestionOption {
  text: string;
  is_correct?: boolean;
}

// Direct exports of Supabase table types
export type Quiz = Tables<"quizzes">;
export type QuizQuestion = Tables<"quiz_questions">;

// Helper types for working with quiz options
export interface McqOption {
  text: string;
  is_correct: boolean;
}

export type QuestionType = Database["public"]["Enums"]["question_type"];

export interface GenerateQuizParams {
  textContent: string;
  documentId?: string | null;
  documentIds?: string[];
  quizTitle: string;
  numberOfQuestions: number;
  aiConfig: AIProviderConfig;
  generationOptions?: { numberOfQuestions?: number; questionTypes: string[] };
  customPrompt?: string;
}

export interface GenerateQuizResponse {
  success: boolean;
  message?: string;
  quiz?: Quiz;
  quizId?: string;
}

export interface GetAllQuizzesApiResponse {
  quizzes: Quiz[];
}

// Type for the form in UploadSection or a dedicated quiz creation form
export interface QuizGenerationFormInputs {
  quizTitle: string;
  numberOfQuestions: number;
}

export interface GenerateQuizParamsFromUpload {
  textContent: string;
  documentId?: string | null;
  quizTitle: string;
  numberOfQuestions: number;
  customPrompt?: string;
  aiConfig: AIProviderConfig;
}

export interface QuizQuestionBatchInsertPayload {
  question_text: string;
  type: string;
  options?: QuizQuestionOption[] | null;
  correct_answer?: string | null;
  explanation?: string | null;
}
