-- Migration to create the study_documents table

CREATE TABLE IF NOT EXISTS public.study_documents (
  id UUID NOT NULL DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  content_type TEXT,
  size_bytes INTEGER,
  status TEXT DEFAULT 'pending',
  extracted_text_path TEXT,
  extracted_text_summary TEXT,
  storage_object_id TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  CONSTRAINT study_documents_pkey PRIMARY KEY (id)
);

-- Enable RLS
ALTER TABLE public.study_documents ENABLE ROW LEVEL SECURITY;

-- Policies for study_documents table
-- Users can select their own documents
CREATE POLICY "Users can select their own documents" ON public.study_documents FOR SELECT
  USING (auth.uid() = user_id);

-- Users can insert their own documents
CREATE POLICY "Users can insert their own documents" ON public.study_documents FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Users can update their own documents
CREATE POLICY "Users can update their own documents" ON public.study_documents FOR UPDATE
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Users can delete their own documents
CREATE POLICY "Users can delete their own documents" ON public.study_documents FOR DELETE
  USING (auth.uid() = user_id);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_study_documents_user_id ON public.study_documents (user_id);
CREATE INDEX IF NOT EXISTS idx_study_documents_status ON public.study_documents (status);
CREATE INDEX IF NOT EXISTS idx_study_documents_created_at ON public.study_documents (created_at);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_study_documents_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update updated_at on row update
CREATE TRIGGER on_study_documents_updated 
  BEFORE UPDATE ON public.study_documents
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_study_documents_updated_at(); 