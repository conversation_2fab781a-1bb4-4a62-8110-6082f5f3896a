import * as pdfjsLib from 'pdfjs-dist';
import { Document } from '@/types';

// Use vanilla JS to load the worker script from CDN
// This resolves issues with bundling the worker
// Updated to use cdnjs and a stable version
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.4.168/pdf.worker.min.mjs';

/**
 * Extract text from a PDF file
 */
export async function extractTextFromPdf(file: File): Promise<Document> {
  try {
    console.log('Starting PDF extraction process...');
    
    // Convert file to ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();
    console.log('File converted to ArrayBuffer');
    
    // Worker is now set at the top of the file
    
    // Load PDF document
    console.log('Creating PDF loading task');
    const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });
    console.log('Waiting for PDF to load...');
    const pdf = await loadingTask.promise;
    console.log(`PDF loaded successfully with ${pdf.numPages} pages`);
    
    // Extract text from all pages
    let fullText = '';
    for (let i = 1; i <= pdf.numPages; i++) {
      console.log(`Processing page ${i} of ${pdf.numPages}`);
      const page = await pdf.getPage(i);
      const textContent = await page.getTextContent();
      const pageText = textContent.items
        .map((item: any) => item.str)
        .join(' ');
      
      fullText += pageText + '\n\n';
    }
    
    console.log('PDF text extraction complete');
    return {
      id: crypto.randomUUID(),
      name: file.name,
      content: fullText.trim(),
      type: 'pdf',
      createdAt: Date.now(),
      size: file.size
    };
  } catch (error) {
    console.error('Error extracting text from PDF:', error);
    
    // More detailed error message based on the type of error
    if (error instanceof Error) {
      if (error.message.includes('version')) {
        throw new Error('PDF processing error: Version mismatch between PDF.js components. Please try again or contact support.');
      } else if (error.message.includes('worker')) {
        throw new Error('PDF processing error: Failed to load PDF worker. Please check your internet connection and try again.');
      } else if (error.message.includes('password')) {
        throw new Error('This PDF is password protected. Please remove the password protection and try again.');
      }
    }
    
    throw new Error('Failed to extract text from PDF. Please try another file or format.');
  }
}
