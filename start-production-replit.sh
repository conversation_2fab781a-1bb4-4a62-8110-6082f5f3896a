
#!/usr/bin/env bash
set -euo pipefail

echo "🚀 Starting ChewyAI Production on Replit..."

# Check if build files exist
if [ ! -f "dist/index.js" ]; then
  echo "❌ Backend build not found. Building..."
  npm run build:server
fi

if [ ! -f "dist/frontend-server.js" ]; then
  echo "❌ Frontend server build not found. Building..."
  npm run build:frontend
fi

if [ ! -d "dist/public" ]; then
  echo "❌ Frontend build not found. Building..."
  npm run build:client
fi

echo "✅ All build files ready"

# Set production environment
export NODE_ENV=production
export PORT=5000
export FRONTEND_PORT=3000

# Start both servers concurrently
echo "🔧 Starting backend server on port 5000..."
echo "🌐 Starting frontend server on port 3000..."

# Start backend first, then frontend
node dist/index.js &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 2

# Start frontend server
node dist/frontend-server.js &
FRONTEND_PID=$!

# Wait for both processes
wait $BACKEND_PID $FRONTEND_PID
