export const animations = {
  // Standard transitions
  fast: 'transition-all duration-150 ease-in-out',
  normal: 'transition-all duration-200 ease-in-out',
  slow: 'transition-all duration-300 ease-in-out',
  
  // Scale animations
  scaleOnHover: 'hover:scale-105 active:scale-95',
  scaleOnPress: 'active:scale-95',
  
  // Fade animations
  fadeIn: 'animate-in fade-in',
  fadeOut: 'animate-out fade-out',
  slideInFromTop: 'animate-in slide-in-from-top-2',
  slideInFromBottom: 'animate-in slide-in-from-bottom-2',
  slideInFromLeft: 'animate-in slide-in-from-left-2',
  slideInFromRight: 'animate-in slide-in-from-right-2',
  
  // Loading animations
  spin: 'animate-spin',
  pulse: 'animate-pulse',
  bounce: 'animate-bounce',
  
  // Card animations
  cardHover: 'hover:shadow-lg hover:-translate-y-1',
  cardPress: 'active:translate-y-0',
  
  // Focus animations
  focusRing: 'focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-slate-900',
  
  // Smooth reveal
  reveal: 'opacity-0 translate-y-4 animate-in fade-in-0 slide-in-from-bottom-4 duration-500',
  
  // Stagger delays for lists
  stagger: {
    delay100: 'animation-delay-100',
    delay200: 'animation-delay-200',
    delay300: 'animation-delay-300',
    delay400: 'animation-delay-400',
    delay500: 'animation-delay-500',
  }
};

export const animationConfig = {
  // Durations in milliseconds
  durations: {
    fast: 150,
    normal: 200,
    slow: 300,
    verySlow: 500,
  },
  
  // Easing functions
  easing: {
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
    linear: 'linear',
  },
  
  // Spring configs for framer-motion if used
  spring: {
    gentle: { stiffness: 300, damping: 30 },
    bouncy: { stiffness: 400, damping: 25 },
    smooth: { stiffness: 200, damping: 20 },
  }
};

export type AnimationVariant = keyof typeof animations;

export const getAnimationClasses = (...variants: AnimationVariant[]): string => {
  return variants.map(variant => animations[variant]).join(' ');
};

export const createStaggeredAnimation = (itemCount: number, baseDelay = 100) => {
  return Array.from({ length: itemCount }, (_, index) => ({
    animationDelay: `${index * baseDelay}ms`,
  }));
};

export const animateOnScroll = (element: HTMLElement, animationClass: string) => {
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add(animationClass);
          observer.unobserve(entry.target);
        }
      });
    },
    { threshold: 0.1 }
  );
  
  observer.observe(element);
  return () => observer.disconnect();
}; 