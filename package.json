{"name": "chewy-ai", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "npm run build:server && npm run build:client", "build:server": "tsc -p server/tsconfig.json", "build:client": "cd client && npm run build", "dev": "bash start-dev-clean.sh", "server": "tsx server/index.ts", "client": "cd client && npm run dev", "start": "npm run server", "test": "jest", "test:watch": "jest --watch", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@hono/node-server": "^1.13.1", "@supabase/supabase-js": "^2.39.3", "cors": "^2.8.5", "dotenv": "^16.4.1", "express": "^4.18.2", "hono": "^4.6.3", "multer": "^1.4.5-lts.1", "tsx": "^4.7.0", "typescript": "^5.2.2"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/multer": "^1.4.11", "@types/node": "^20.11.5", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.3", "ts-jest": "^29.1.2", "ts-node": "^10.9.2"}}