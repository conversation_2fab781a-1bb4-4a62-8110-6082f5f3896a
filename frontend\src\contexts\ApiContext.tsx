import React, { createContext, useContext } from "react";
import axios, { AxiosInstance, AxiosRequestConfig } from "axios";

interface ApiContextValue {
  client: AxiosInstance;
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T>;
  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;
  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;
  del<T = any>(url: string, config?: AxiosRequestConfig): Promise<T>;
}

// Create axios instance with base URL from env
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || "/api";

const axiosInstance = axios.create({
  baseURL: apiBaseUrl,
  withCredentials: true, // send cookies for auth
});

// Simple helpers that unwrap data
const unwrap = <T,>(p: Promise<{ data: T }>) => p.then((r) => r.data);

const defaultValue: ApiContextValue = {
  client: axiosInstance,
  get: (url, config) => unwrap(axiosInstance.get(url, config)),
  post: (url, data, config) => unwrap(axiosInstance.post(url, data, config)),
  put: (url, data, config) => unwrap(axiosInstance.put(url, data, config)),
  del: (url, config) => unwrap(axiosInstance.delete(url, config)),
};

const ApiContext = createContext<ApiContextValue>(defaultValue);

export const ApiProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <ApiContext.Provider value={defaultValue}>{children}</ApiContext.Provider>;
};

export const useApi = () => useContext(ApiContext);
