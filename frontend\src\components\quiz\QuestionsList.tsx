import React, { useState } from "react";
import { Tables } from "../../types/supabase";
// Removed Supabase import - using backend API endpoints
import { useAuth } from "../../hooks/useAuth";
import { Button } from "@/components/ui/button";
import { QuestionForm } from "./QuestionForm";

type QuizQuestion = Tables<"quiz_questions">;

interface McqOption {
  text: string;
  is_correct: boolean;
}

export interface QuestionsListProps {
  questions: QuizQuestion[];
  loading: boolean;
  onRefreshQuestions: () => void;
  selectedQuizId: string;
}

export const QuestionsList: React.FC<QuestionsListProps> = ({
  questions,
  loading,
  onRefreshQuestions,
  selectedQuizId,
}) => {
  const { user } = useAuth();
  const [editingQuestionId, setEditingQuestionId] = useState<string | null>(null);

  const handleDeleteQuestion = async (questionId: string) => {
    if (
      !window.confirm(
        "Are you sure you want to delete this question? This action cannot be undone."
      )
    )
      return;

    try {
      // Delete question via backend API
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/quiz-questions/${questionId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete question');
      }

      onRefreshQuestions();
    } catch (err: any) {
      console.error("Error deleting question:", err);
      alert(`Error deleting question: ${err.message}`);
    }
  };

  const handleEditClick = (questionId: string) => {
    setEditingQuestionId(questionId);
  };

  const handleSaveOrCancelEdit = () => {
    setEditingQuestionId(null);
    onRefreshQuestions();
  };

  if (loading) {
    return (
      <div className="bg-slate-800 border border-slate-700 rounded-xl p-6 shadow-lg">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-400 mx-auto mb-4"></div>
          <p className="text-slate-300">Loading questions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-slate-800 border border-slate-700 rounded-xl p-6 shadow-lg">
      <div className="flex items-center justify-between border-b border-slate-700 pb-4 mb-6">
        <h5 className="text-lg font-bold text-slate-200 flex items-center">
          <svg
            className="w-5 h-5 mr-2 text-purple-400"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
              clipRule="evenodd"
            />
          </svg>
          Quiz Questions
        </h5>
        <span className="text-slate-400 text-sm bg-slate-900 px-3 py-1 rounded-lg">
          {questions.length} question{questions.length !== 1 ? "s" : ""}
        </span>
      </div>

      {questions.length === 0 ? (
        <div className="text-center py-12">
          <svg
            className="w-12 h-12 text-slate-500 mx-auto mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <p className="text-slate-400 text-lg mb-2">No questions yet</p>
          <p className="text-slate-500 text-sm">
            Add your first question or generate questions from a document
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {questions.map((q, index) => (
            <div
              key={q.id}
              className="bg-slate-900 border border-slate-600 rounded-lg p-4 hover:border-slate-500 transition-colors"
            >
              {editingQuestionId === q.id ? (
                <QuestionForm
                  selectedQuizId={selectedQuizId}
                  editingQuestion={q}
                  onQuestionSaved={handleSaveOrCancelEdit}
                  onCancel={handleSaveOrCancelEdit}
                />
              ) : (
                <>
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="text-slate-400 font-mono text-sm bg-slate-800 px-2 py-1 rounded">
                          Q{index + 1}
                        </span>
                        <span
                          className={`text-xs px-2 py-1 rounded-full ${
                            q.type === "multiple_choice"
                              ? "bg-blue-900/30 text-blue-400"
                              : q.type === "select_all_that_apply"
                              ? "bg-green-900/30 text-green-400"
                              : q.type === "true_false"
                              ? "bg-yellow-900/30 text-yellow-400"
                              : q.type === "short_answer"
                              ? "bg-orange-900/30 text-orange-400"
                              : "bg-slate-900/30 text-slate-400"
                          }`}
                        >
                          {q.type
                            .replace(/_/g, " ")
                            .replace(/\b\w/g, (l) => l.toUpperCase())}
                        </span>
                      </div>
                      <p className="font-medium text-slate-200 mb-3">
                        {q.question_text}
                      </p>

                      {/* Display options/answer based on type */}
                      {(q.type === "multiple_choice" ||
                        q.type === "select_all_that_apply") &&
                        q.options &&
                        Array.isArray(q.options) && (
                          <div className="bg-slate-800/50 rounded-lg p-3 mb-3">
                            <p className="text-xs text-slate-400 mb-2 font-medium">
                              Options:
                            </p>
                            <div className="space-y-1">
                              {(q.options as unknown as McqOption[]).map((opt, i) => (
                                <div
                                  key={i}
                                  className="flex items-center space-x-2 text-sm"
                                >
                                  <span className="text-slate-500 font-mono w-6">
                                    {q.type === "multiple_choice"
                                      ? String.fromCharCode(65 + i)
                                      : i + 1}
                                    .
                                  </span>
                                  <span
                                    className={
                                      opt.is_correct
                                        ? "text-green-400 font-medium"
                                        : "text-slate-300"
                                    }
                                  >
                                    {opt.text}
                                  </span>
                                  {opt.is_correct && (
                                    <svg
                                      className="w-4 h-4 text-green-400"
                                      fill="currentColor"
                                      viewBox="0 0 20 20"
                                    >
                                      <path
                                        fillRule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clipRule="evenodd"
                                      />
                                    </svg>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                      {(q.type === "true_false" || q.type === "short_answer") &&
                        q.correct_answer && (
                          <div className="bg-slate-800/50 rounded-lg p-3 mb-3">
                            <p className="text-xs text-slate-400 mb-1 font-medium">
                              Correct Answer:
                            </p>
                            <span className="text-green-400 font-medium text-sm">
                              {q.correct_answer}
                            </span>
                          </div>
                        )}

                      {q.explanation && (
                        <div className="bg-indigo-900/20 border border-indigo-500/30 rounded-lg p-3 mb-3">
                          <p className="text-xs text-indigo-400 mb-1 font-medium">
                            Explanation:
                          </p>
                          <p className="text-slate-300 text-sm">{q.explanation}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center justify-end space-x-2 pt-3 border-t border-slate-700">
                    <Button
                      onClick={() => handleEditClick(q.id)}
                      size="sm"
                      className="bg-purple-600 hover:bg-purple-700 text-white"
                    >
                      <svg
                        className="w-4 h-4 mr-1"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                      </svg>
                      Edit
                    </Button>
                    <Button
                      onClick={() => handleDeleteQuestion(q.id)}
                      variant="destructive"
                      size="sm"
                    >
                      <svg
                        className="w-4 h-4 mr-1"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"
                          clipRule="evenodd"
                        />
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 012 0v4a1 1 0 11-2 0V7zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V7a1 1 0 00-1-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                      Delete
                    </Button>
                  </div>
                </>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
